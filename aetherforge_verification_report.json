{"timestamp": "2025-06-20T11:14:14.156128", "overall_completion": 84.96133023975251, "components": {"orchestrator": {"name": "orchestrator", "path": "src\\orchestrator.py", "exists": true, "lines_of_code": 3074, "todo_count": 0, "stub_count": 0, "incomplete_count": 0, "test_coverage": 50.0, "completion_percentage": 85.0, "issues": ["Line 49: mock - # Mock load_dotenv if not available", "Line 437: NOTE:?\\s*(.+) - Note:", "Line 1505: NOTE:?\\s*(.+) - \"notes\": phase.notes", "Empty function: load_dotenv"], "features": ["Central coordination service"]}, "pheromone_system": {"name": "pheromone_system", "path": "src\\pheromone_system.py", "exists": true, "lines_of_code": 294, "todo_count": 0, "stub_count": 0, "incomplete_count": 0, "test_coverage": 50.0, "completion_percentage": 85.0, "issues": [], "features": ["Real-time agent communication"]}, "workflow_engine": {"name": "workflow_engine", "path": "src\\workflow_engine.py", "exists": true, "lines_of_code": 2291, "todo_count": 0, "stub_count": 0, "incomplete_count": 0, "test_coverage": 50.0, "completion_percentage": 85.0, "issues": ["Line 212: NOTE:?\\s*(.+) - notes: str = \"\"", "Line 226: NOTE:?\\s*(.+) - \"notes\": self.notes", "Line 699: NOTE:?\\s*(.+) - notes=phase_data.get('notes', ''),", "Line 2121: mock - # Return mock success result", "Line 2411: NOTE:?\\s*(.+) - description=step.get('notes', ''),", "Line 2417: NOTE:?\\s*(.+) - notes=step.get('notes', ''),", "Line 2425: NOTE:?\\s*(.+) - description=step.get('notes', ''),", "Line 2431: NOTE:?\\s*(.+) - notes=step.get('notes', '')", "Line 2663: mock - # Fallback to mock execution", "Line 2664: mock - logger.warning(f\"Agent executors not available, using mock execution for phase {phase.name}\")"], "features": ["BMAD workflow execution"]}, "agent_executors": {"name": "agent_executors", "path": "src\\agent_executors.py", "exists": true, "lines_of_code": 682, "todo_count": 0, "stub_count": 0, "incomplete_count": 0, "test_coverage": 50.0, "completion_percentage": 85.0, "issues": [], "features": ["AI agent execution system"]}, "project_generator": {"name": "project_generator", "path": "src\\project_generator_standalone.py", "exists": true, "lines_of_code": 658, "todo_count": 0, "stub_count": 0, "incomplete_count": 0, "test_coverage": 50.0, "completion_percentage": 85.0, "issues": [], "features": ["Project generation engine"]}, "component_adapters": {"name": "component_adapters", "path": "src\\component_adapters_real.py", "exists": true, "lines_of_code": 318, "todo_count": 0, "stub_count": 0, "incomplete_count": 0, "test_coverage": 50.0, "completion_percentage": 85.0, "issues": [], "features": ["External service integration"]}, "config_manager": {"name": "config_manager", "path": "src\\config_manager.py", "exists": true, "lines_of_code": 320, "todo_count": 0, "stub_count": 0, "incomplete_count": 0, "test_coverage": 50.0, "completion_percentage": 85.0, "issues": [], "features": ["Configuration management"]}, "vscode_extension": {"name": "vscode_extension", "path": "src\\aetherforge.ts", "exists": true, "lines_of_code": 1293, "todo_count": 0, "stub_count": 4, "incomplete_count": 0, "test_coverage": 50.0, "completion_percentage": 84.6906419180201, "issues": ["Line 749: placeholder - <textarea id=\"prompt\" placeholder=\"Describe the software project you want to create in detail. Include features, technologies, and any specific requirements...\"></textarea>", "Line 754: placeholder - <input type=\"text\" id=\"projectName\" placeholder=\"MyAwesomeProject\">", "Line 826: placeholder - <input type=\"text\" id=\"orchestratorUrl\" value=\"http://localhost:8000\" placeholder=\"http://localhost:8000\">", "Line 830: placeholder - <input type=\"text\" id=\"projectsPath\" value=\"./projects\" placeholder=\"./projects\">", "Syntax error in file"], "features": ["VS Code extension"]}}, "functionality_tests": {"orchestrator_api": true, "pheromone_system": true, "workflow_engine": true, "agent_system": true, "project_generation": false, "vscode_extension": true}, "integration_tests": {"orchestrator_pheromone": true, "workflow_agent": true, "project_generation": false, "vscode_extension": true}, "recommendations": ["Fix integration issue: project_generation"]}