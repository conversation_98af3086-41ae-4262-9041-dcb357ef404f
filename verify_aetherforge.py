#!/usr/bin/env python3
"""
Comprehensive Aetherforge System Verification Script

This script performs a detailed analysis of the Aetherforge codebase to:
1. Scan all components and assess completion status
2. Check for TODOs, stubs, and incomplete implementations
3. Test core functionalities and integrations
4. Generate detailed HTML and console reports
5. Provide completion percentages and recommendations

Usage: python verify_aetherforge.py
"""

import os
import sys
import json
import re
import ast
import subprocess
import asyncio
import time
import requests
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict
import html

@dataclass
class ComponentStatus:
    """Status information for a component"""
    name: str
    path: str
    exists: bool = False
    lines_of_code: int = 0
    todo_count: int = 0
    stub_count: int = 0
    incomplete_count: int = 0
    test_coverage: float = 0.0
    completion_percentage: float = 0.0
    issues: List[str] = None
    features: List[str] = None
    
    def __post_init__(self):
        if self.issues is None:
            self.issues = []
        if self.features is None:
            self.features = []

@dataclass
class SystemReport:
    """Complete system verification report"""
    timestamp: str
    overall_completion: float
    components: Dict[str, ComponentStatus]
    functionality_tests: Dict[str, bool]
    integration_tests: Dict[str, bool]
    recommendations: List[str]
    missing_features: List[str]

class AetherforgeVerifier:
    """Main verification class"""
    
    def __init__(self):
        self.root_path = Path.cwd()
        self.src_path = self.root_path / "src"
        self.components = {}
        self.test_results = {}
        self.report = None
        
        # Define expected components and their key files
        self.expected_components = {
            "orchestrator": {
                "main_file": "src/orchestrator.py",
                "test_file": "tests/test_orchestrator.py",
                "description": "Central coordination service"
            },
            "pheromone_system": {
                "main_file": "src/pheromone_system.py", 
                "test_file": "tests/test_pheromone_system.py",
                "description": "Real-time agent communication"
            },
            "workflow_engine": {
                "main_file": "src/workflow_engine.py",
                "test_file": "tests/test_workflow_engine.py", 
                "description": "BMAD workflow execution"
            },
            "agent_executors": {
                "main_file": "src/agent_executors.py",
                "test_file": "tests/test_agent_executors_comprehensive.py",
                "description": "AI agent execution system"
            },
            "project_generator": {
                "main_file": "src/project_generator_standalone.py",
                "test_file": "tests/test_integration.py",
                "description": "Project generation engine"
            },
            "component_adapters": {
                "main_file": "src/component_adapters_real.py",
                "test_file": "tests/test_archon_integration.py",
                "description": "External service integration"
            },
            "config_manager": {
                "main_file": "src/config_manager.py",
                "test_file": "tests/conftest.py",
                "description": "Configuration management"
            },
            "vscode_extension": {
                "main_file": "src/aetherforge.ts",
                "test_file": "tests/test_vscode_extension.py",
                "description": "VS Code extension"
            }
        }
        
        # Patterns to identify incomplete code
        self.incomplete_patterns = [
            r'TODO:?\s*(.+)',
            r'FIXME:?\s*(.+)', 
            r'XXX:?\s*(.+)',
            r'HACK:?\s*(.+)',
            r'NOTE:?\s*(.+)',
            r'def\s+\w+\([^)]*\):\s*pass\s*$',
            r'def\s+\w+\([^)]*\):\s*\.\.\.\s*$',
            r'raise\s+NotImplementedError',
            r'NotImplemented',
            r'placeholder',
            r'stub',
            r'mock',
            r'dummy'
        ]

    def print_status(self, message: str, status: str = "INFO"):
        """Print formatted status message"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        symbols = {
            "INFO": "ℹ️", "SUCCESS": "✅", "ERROR": "❌", 
            "WARNING": "⚠️", "RUNNING": "🔄", "ANALYSIS": "🔍"
        }
        symbol = symbols.get(status, "•")
        print(f"[{timestamp}] {symbol} {message}")

    def analyze_file(self, file_path: Path) -> Tuple[int, List[str]]:
        """Analyze a file for completeness and issues"""
        if not file_path.exists():
            return 0, ["File does not exist"]
        
        try:
            content = file_path.read_text(encoding='utf-8', errors='ignore')
            lines = content.split('\n')
            line_count = len([line for line in lines if line.strip()])
            
            issues = []
            
            # Check for incomplete patterns
            for i, line in enumerate(lines, 1):
                for pattern in self.incomplete_patterns:
                    matches = re.findall(pattern, line, re.IGNORECASE)
                    if matches:
                        issues.append(f"Line {i}: {pattern} - {line.strip()}")
            
            # Check for empty functions/classes
            try:
                tree = ast.parse(content)
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        if (len(node.body) == 1 and 
                            isinstance(node.body[0], (ast.Pass, ast.Ellipsis))):
                            issues.append(f"Empty function: {node.name}")
                    elif isinstance(node, ast.ClassDef):
                        if (len(node.body) == 1 and 
                            isinstance(node.body[0], (ast.Pass, ast.Ellipsis))):
                            issues.append(f"Empty class: {node.name}")
            except SyntaxError:
                issues.append("Syntax error in file")
            
            return line_count, issues
            
        except Exception as e:
            return 0, [f"Error reading file: {str(e)}"]

    def calculate_completion_percentage(self, component: ComponentStatus) -> float:
        """Calculate completion percentage for a component"""
        if not component.exists:
            return 0.0
        
        # Base score for existing file
        score = 50.0
        
        # Deduct points for issues
        if component.lines_of_code > 0:
            issue_ratio = (component.todo_count + component.stub_count + 
                          component.incomplete_count) / component.lines_of_code
            score -= min(issue_ratio * 100, 40.0)
        
        # Add points for substantial implementation
        if component.lines_of_code > 100:
            score += 20.0
        elif component.lines_of_code > 50:
            score += 10.0
        
        # Add points for test coverage
        score += component.test_coverage * 0.3
        
        return min(max(score, 0.0), 100.0)

    def scan_components(self):
        """Scan all components and analyze their status"""
        self.print_status("Scanning Aetherforge components...", "ANALYSIS")
        
        for comp_name, comp_info in self.expected_components.items():
            self.print_status(f"Analyzing {comp_name}...", "RUNNING")
            
            main_file = Path(comp_info["main_file"])
            test_file = Path(comp_info["test_file"])
            
            # Analyze main file
            lines_of_code, issues = self.analyze_file(main_file)
            
            # Count specific issue types
            todo_count = len([i for i in issues if 'TODO' in i.upper()])
            stub_count = len([i for i in issues if any(word in i.lower() 
                            for word in ['stub', 'pass', 'placeholder'])])
            incomplete_count = len([i for i in issues if any(word in i.lower() 
                                  for word in ['notimplemented', 'fixme', 'xxx'])])
            
            # Check test coverage (simplified)
            test_coverage = 50.0 if test_file.exists() else 0.0
            
            # Create component status
            component = ComponentStatus(
                name=comp_name,
                path=str(main_file),
                exists=main_file.exists(),
                lines_of_code=lines_of_code,
                todo_count=todo_count,
                stub_count=stub_count,
                incomplete_count=incomplete_count,
                test_coverage=test_coverage,
                issues=issues[:10],  # Limit to first 10 issues
                features=[comp_info["description"]]
            )
            
            component.completion_percentage = self.calculate_completion_percentage(component)
            self.components[comp_name] = component
            
            status = "SUCCESS" if component.completion_percentage > 70 else "WARNING"
            self.print_status(f"{comp_name}: {component.completion_percentage:.1f}% complete", status)

    async def test_orchestrator_api(self) -> bool:
        """Test orchestrator API endpoints"""
        self.print_status("Testing Orchestrator API...", "RUNNING")
        
        try:
            # Start orchestrator in background
            process = subprocess.Popen([
                sys.executable, "-m", "uvicorn", "src.orchestrator:app",
                "--host", "0.0.0.0", "--port", "8000"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Wait for startup
            await asyncio.sleep(8)
            
            # Test endpoints
            endpoints = [
                "/health",
                "/components/status", 
                "/pheromones/statistics",
                "/projects",
                "/docs"
            ]
            
            results = {}
            for endpoint in endpoints:
                try:
                    response = requests.get(f"http://localhost:8000{endpoint}", timeout=5)
                    results[endpoint] = response.status_code == 200
                    self.print_status(f"Endpoint {endpoint}: {'✅' if results[endpoint] else '❌'}")
                except Exception as e:
                    results[endpoint] = False
                    self.print_status(f"Endpoint {endpoint}: ❌ ({str(e)})")
            
            # Cleanup
            process.terminate()
            try:
                await asyncio.wait_for(process.wait(), timeout=5)
            except:
                process.kill()
            
            return all(results.values())
            
        except Exception as e:
            self.print_status(f"Orchestrator test failed: {e}", "ERROR")
            return False

    async def test_pheromone_system(self) -> bool:
        """Test pheromone communication system"""
        self.print_status("Testing Pheromone System...", "RUNNING")

        try:
            sys.path.insert(0, 'src')
            from pheromone_system import RealTimePheromoneSystem

            with tempfile.TemporaryDirectory() as temp_dir:
                test_file = os.path.join(temp_dir, "test_pheromones.json")
                pheromone_system = RealTimePheromoneSystem(test_file)

                # Start system
                await pheromone_system.start()

                # Test message dropping
                message_id = await pheromone_system.drop_pheromone(
                    "test_verification", {"test": "data"}, "verify_project_123"
                )

                # Test statistics
                stats = pheromone_system.get_statistics()

                # Test project messages
                project_messages = pheromone_system.get_project_messages("verify_project_123")

                # Stop system
                await pheromone_system.stop()

                success = (message_id is not None and
                          stats['total_messages'] > 0 and
                          len(project_messages) > 0)

                self.print_status(f"Pheromone system: {'✅' if success else '❌'}")
                return success

        except Exception as e:
            self.print_status(f"Pheromone test failed: {e}", "ERROR")
            return False

    async def test_workflow_engine(self) -> bool:
        """Test workflow engine functionality"""
        self.print_status("Testing Workflow Engine...", "RUNNING")

        try:
            sys.path.insert(0, 'src')
            from workflow_engine import WorkflowEngine

            # Create workflow engine
            engine = WorkflowEngine()

            # Test workflow loading - use correct method name
            workflows = engine.workflows  # Direct access to workflows dict
            workflow_count = len(workflows)

            # Test specific workflow
            if "greenfield-fullstack" in workflows:
                workflow = workflows["greenfield-fullstack"]
                has_steps = (hasattr(workflow, 'steps') and len(workflow.steps) > 0) if workflow else False
            else:
                has_steps = False

            success = workflow_count > 0
            self.print_status(f"Workflow engine: {'✅' if success else '❌'} ({workflow_count} workflows)")
            return success

        except Exception as e:
            self.print_status(f"Workflow test failed: {e}", "ERROR")
            return False

    async def test_agent_system(self) -> bool:
        """Test agent execution system"""
        self.print_status("Testing Agent System...", "RUNNING")

        try:
            # Add both src and root to path
            import sys
            sys.path.insert(0, 'src')
            sys.path.insert(0, '.')

            # Test if agent executors module exists and has expected functions
            try:
                import agent_executors
                has_module = True
            except ImportError:
                try:
                    from src import agent_executors
                    has_module = True
                except ImportError:
                    has_module = False

            if has_module:
                # Check for expected functions
                expected_functions = [
                    'execute_analyst_work', 'execute_architect_work',
                    'execute_developer_work', 'execute_qa_work'
                ]

                available_functions = [hasattr(agent_executors, func) for func in expected_functions]
                all_available = all(available_functions)

                self.print_status(f"Agent system: {'✅' if all_available else '❌'} ({sum(available_functions)}/{len(expected_functions)} functions)")
                return all_available
            else:
                self.print_status("Agent system: ❌ (module not found)")
                return False

        except Exception as e:
            self.print_status(f"Agent test failed: {e}", "ERROR")
            return False

    async def test_project_generation(self) -> bool:
        """Test project generation functionality"""
        self.print_status("Testing Project Generation...", "RUNNING")

        try:
            sys.path.insert(0, 'src')
            from project_generator_standalone import ProjectGenerator

            # Create test generator with correct constructor
            generator = ProjectGenerator()  # No base_path parameter

            # Test project creation with correct method signature
            with tempfile.TemporaryDirectory() as temp_dir:
                project_data = {
                    "prompt": "Create a simple test application",
                    "name": "VerificationTest",
                    "type": "web_application",
                    "description": "Test project for verification"
                }

                # Use the correct method signature
                result = await generator.generate_project(
                    prompt=project_data["prompt"],
                    project_name=project_data["name"],
                    project_type=project_data["type"],
                    project_path=temp_dir
                )

                # Check if files were created
                project_path = Path(temp_dir) / "VerificationTest"
                files_created = list(project_path.rglob("*")) if project_path.exists() else []

                success = len(files_created) > 3  # Should create some files
                self.print_status(f"Project generation: {'✅' if success else '❌'} ({len(files_created)} files)")
                return success

        except Exception as e:
            self.print_status(f"Project generation test failed: {e}", "ERROR")
            return False

    def test_vscode_extension(self) -> bool:
        """Test VS Code extension files"""
        self.print_status("Testing VS Code Extension...", "RUNNING")

        try:
            # Check main extension files
            extension_files = [
                "src/aetherforge.ts",
                "vscode-extension/package.json",
                "vscode-extension/src/extension.ts"
            ]

            files_exist = []
            for file_path in extension_files:
                path = Path(file_path)
                exists = path.exists()
                files_exist.append(exists)
                self.print_status(f"Extension file {file_path}: {'✅' if exists else '❌'}")

            # Check if extension is built
            built_files = [
                "vscode-extension/out/extension.js",
                "dist/extension.js"
            ]

            built_exists = any(Path(f).exists() for f in built_files)

            success = any(files_exist) and built_exists
            self.print_status(f"VS Code extension: {'✅' if success else '❌'}")
            return success

        except Exception as e:
            self.print_status(f"VS Code extension test failed: {e}", "ERROR")
            return False

    async def test_integrations(self) -> Dict[str, bool]:
        """Test integration between components"""
        self.print_status("Testing Component Integrations...", "RUNNING")

        integration_results = {}

        # Test orchestrator-pheromone integration
        try:
            orchestrator_test = await self.test_orchestrator_api()
            pheromone_test = await self.test_pheromone_system()
            integration_results["orchestrator_pheromone"] = orchestrator_test and pheromone_test
        except:
            integration_results["orchestrator_pheromone"] = False

        # Test workflow-agent integration
        try:
            workflow_test = await self.test_workflow_engine()
            agent_test = await self.test_agent_system()
            integration_results["workflow_agent"] = workflow_test and agent_test
        except:
            integration_results["workflow_agent"] = False

        # Test project generation integration
        try:
            integration_results["project_generation"] = await self.test_project_generation()
        except:
            integration_results["project_generation"] = False

        # Test VS Code integration
        try:
            integration_results["vscode_extension"] = self.test_vscode_extension()
        except:
            integration_results["vscode_extension"] = False

        return integration_results

    def generate_recommendations(self) -> List[str]:
        """Generate recommendations based on analysis"""
        recommendations = []

        # Check overall completion
        total_completion = sum(comp.completion_percentage for comp in self.components.values())
        avg_completion = total_completion / len(self.components) if self.components else 0

        if avg_completion < 70:
            recommendations.append("System completion is below 70%. Focus on completing core components.")

        # Component-specific recommendations
        for name, component in self.components.items():
            if not component.exists:
                recommendations.append(f"Create missing component: {name}")
            elif component.completion_percentage < 50:
                recommendations.append(f"Complete implementation of {name} (currently {component.completion_percentage:.1f}%)")
            elif component.todo_count > 5:
                recommendations.append(f"Address {component.todo_count} TODOs in {name}")
            elif component.test_coverage < 30:
                recommendations.append(f"Add comprehensive tests for {name}")

        # Integration recommendations
        if hasattr(self, 'integration_results'):
            failed_integrations = [k for k, v in self.integration_results.items() if not v]
            for integration in failed_integrations:
                recommendations.append(f"Fix integration issue: {integration}")

        return recommendations

    def generate_html_report(self) -> str:
        """Generate detailed HTML report"""

        # Calculate overall metrics
        total_completion = sum(comp.completion_percentage for comp in self.components.values())
        avg_completion = total_completion / len(self.components) if self.components else 0

        total_issues = sum(len(comp.issues) for comp in self.components.values())
        total_lines = sum(comp.lines_of_code for comp in self.components.values())

        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Aetherforge System Verification Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }}
        .summary {{ background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px;
                   box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .component {{ background: white; margin: 10px 0; padding: 15px; border-radius: 8px;
                     box-shadow: 0 2px 5px rgba(0,0,0,0.1); }}
        .progress-bar {{ background: #e0e0e0; border-radius: 10px; overflow: hidden; height: 20px; }}
        .progress-fill {{ height: 100%; transition: width 0.3s ease; }}
        .high {{ background: #4CAF50; }}
        .medium {{ background: #FF9800; }}
        .low {{ background: #F44336; }}
        .metric {{ display: inline-block; margin: 10px 20px 10px 0; }}
        .metric-value {{ font-size: 24px; font-weight: bold; color: #333; }}
        .metric-label {{ font-size: 12px; color: #666; text-transform: uppercase; }}
        .issues {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px;
                  border-radius: 5px; margin: 10px 0; }}
        .recommendations {{ background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px;
                           border-radius: 5px; margin: 20px 0; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
        th {{ background-color: #f8f9fa; font-weight: bold; }}
        .status-good {{ color: #28a745; }}
        .status-warning {{ color: #ffc107; }}
        .status-error {{ color: #dc3545; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🔮 Aetherforge System Verification Report</h1>
        <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>

    <div class="summary">
        <h2>📊 System Overview</h2>
        <div class="metric">
            <div class="metric-value">{avg_completion:.1f}%</div>
            <div class="metric-label">Overall Completion</div>
        </div>
        <div class="metric">
            <div class="metric-value">{len(self.components)}</div>
            <div class="metric-label">Components Analyzed</div>
        </div>
        <div class="metric">
            <div class="metric-value">{total_lines:,}</div>
            <div class="metric-label">Lines of Code</div>
        </div>
        <div class="metric">
            <div class="metric-value">{total_issues}</div>
            <div class="metric-label">Issues Found</div>
        </div>

        <div class="progress-bar">
            <div class="progress-fill {'high' if avg_completion >= 70 else 'medium' if avg_completion >= 50 else 'low'}"
                 style="width: {avg_completion}%"></div>
        </div>
    </div>
"""

        # Component details
        html_content += """
    <div class="summary">
        <h2>🔧 Component Analysis</h2>
        <table>
            <tr>
                <th>Component</th>
                <th>Status</th>
                <th>Completion</th>
                <th>Lines of Code</th>
                <th>Issues</th>
                <th>Test Coverage</th>
            </tr>
"""

        for name, component in self.components.items():
            status_class = ("status-good" if component.completion_percentage >= 70
                          else "status-warning" if component.completion_percentage >= 50
                          else "status-error")

            html_content += f"""
            <tr>
                <td><strong>{name}</strong><br><small>{component.features[0] if component.features else ''}</small></td>
                <td class="{status_class}">{'✅ Complete' if component.completion_percentage >= 70 else '⚠️ Partial' if component.completion_percentage >= 50 else '❌ Incomplete'}</td>
                <td>
                    <div class="progress-bar" style="width: 100px; height: 15px;">
                        <div class="progress-fill {'high' if component.completion_percentage >= 70 else 'medium' if component.completion_percentage >= 50 else 'low'}"
                             style="width: {component.completion_percentage}%"></div>
                    </div>
                    {component.completion_percentage:.1f}%
                </td>
                <td>{component.lines_of_code:,}</td>
                <td>{len(component.issues)}</td>
                <td>{component.test_coverage:.1f}%</td>
            </tr>
"""

        html_content += """
        </table>
    </div>
"""

        # Recommendations
        recommendations = self.generate_recommendations()
        if recommendations:
            html_content += """
    <div class="recommendations">
        <h2>💡 Recommendations</h2>
        <ul>
"""
            for rec in recommendations:
                html_content += f"            <li>{html.escape(rec)}</li>\n"

            html_content += """
        </ul>
    </div>
"""

        html_content += """
</body>
</html>
"""
        return html_content

    async def run_verification(self):
        """Run complete verification process"""
        self.print_status("🔮 Starting Aetherforge System Verification", "INFO")

        # Step 1: Scan components
        self.scan_components()

        # Step 2: Test functionality
        self.print_status("Testing core functionalities...", "ANALYSIS")

        functionality_tests = {
            "orchestrator_api": await self.test_orchestrator_api(),
            "pheromone_system": await self.test_pheromone_system(),
            "workflow_engine": await self.test_workflow_engine(),
            "agent_system": await self.test_agent_system(),
            "project_generation": await self.test_project_generation(),
            "vscode_extension": self.test_vscode_extension()
        }

        # Step 3: Test integrations
        integration_results = await self.test_integrations()
        self.integration_results = integration_results

        # Step 4: Generate report
        self.print_status("Generating verification report...", "RUNNING")

        # Calculate overall completion
        total_completion = sum(comp.completion_percentage for comp in self.components.values())
        overall_completion = total_completion / len(self.components) if self.components else 0

        # Create system report
        self.report = SystemReport(
            timestamp=datetime.now().isoformat(),
            overall_completion=overall_completion,
            components=self.components,
            functionality_tests=functionality_tests,
            integration_tests=integration_results,
            recommendations=self.generate_recommendations(),
            missing_features=[]
        )

        # Generate HTML report
        html_report = self.generate_html_report()
        report_path = Path("aetherforge_verification_report.html")
        report_path.write_text(html_report, encoding='utf-8')

        # Generate JSON report
        json_report = {
            "timestamp": self.report.timestamp,
            "overall_completion": self.report.overall_completion,
            "components": {name: asdict(comp) for name, comp in self.components.items()},
            "functionality_tests": functionality_tests,
            "integration_tests": integration_results,
            "recommendations": self.report.recommendations
        }

        json_path = Path("aetherforge_verification_report.json")
        json_path.write_text(json.dumps(json_report, indent=2), encoding='utf-8')

        # Print summary
        self.print_summary()

        return self.report

    def print_summary(self):
        """Print verification summary to console"""
        print("\n" + "=" * 70)
        print("  🔮 AETHERFORGE VERIFICATION SUMMARY")
        print("=" * 70)

        # Overall status
        overall = self.report.overall_completion
        status_emoji = "🎉" if overall >= 80 else "⚠️" if overall >= 60 else "❌"
        print(f"\n{status_emoji} Overall System Completion: {overall:.1f}%")

        # Component breakdown
        print(f"\n📊 Component Status:")
        for name, component in self.components.items():
            emoji = "✅" if component.completion_percentage >= 70 else "⚠️" if component.completion_percentage >= 50 else "❌"
            print(f"  {emoji} {name}: {component.completion_percentage:.1f}%")

        # Test results
        print(f"\n🧪 Functionality Tests:")
        for test_name, result in self.report.functionality_tests.items():
            emoji = "✅" if result else "❌"
            print(f"  {emoji} {test_name}: {'PASSED' if result else 'FAILED'}")

        # Integration results
        print(f"\n🔗 Integration Tests:")
        for test_name, result in self.report.integration_tests.items():
            emoji = "✅" if result else "❌"
            print(f"  {emoji} {test_name}: {'PASSED' if result else 'FAILED'}")

        # Recommendations
        if self.report.recommendations:
            print(f"\n💡 Top Recommendations:")
            for i, rec in enumerate(self.report.recommendations[:5], 1):
                print(f"  {i}. {rec}")

        print(f"\n📄 Detailed reports saved:")
        print(f"  • HTML: aetherforge_verification_report.html")
        print(f"  • JSON: aetherforge_verification_report.json")
        print("=" * 70)

async def main():
    """Main verification function"""
    verifier = AetherforgeVerifier()

    try:
        report = await verifier.run_verification()

        # Return appropriate exit code
        if report.overall_completion >= 80:
            return 0
        elif report.overall_completion >= 60:
            return 1
        else:
            return 2

    except KeyboardInterrupt:
        verifier.print_status("Verification interrupted by user", "WARNING")
        return 130
    except Exception as e:
        verifier.print_status(f"Verification failed: {e}", "ERROR")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
