{"test_agent_executors_comprehensive.py": true, "test_complete_integration.py": true, "test_end_to_end.py": true, "test_integration_comprehensive.py": true, "test_performance.py": true, "test_pheromone_system.py": true, "test_workflow_engine.py": true, "test_production_integration.py::TestProductionIntegration::test_docker_compose_validation": true, "test_production_integration.py::TestProductionIntegration::test_dockerfile_validation": true, "test_production_integration.py::TestProductionIntegration::test_service_health_endpoints": true, "test_production_integration.py::TestProductionIntegration::test_component_service_connectivity": true, "test_production_integration.py::TestProductionIntegration::test_environment_variables_validation": true, "test_production_integration.py::TestProductionIntegration::test_database_connectivity": true, "test_production_integration.py::TestProductionIntegration::test_redis_connectivity": true, "test_production_integration.py::TestProductionIntegration::test_ssl_certificate_validation": true, "test_production_integration.py::TestProductionIntegration::test_monitoring_stack_health": true, "test_production_integration.py::TestProductionIntegration::test_load_balancer_configuration": true, "test_production_integration.py::TestProductionIntegration::test_backup_and_recovery_scripts": true, "test_production_integration.py::TestProductionIntegration::test_production_performance_limits": true, "test_production_integration.py::TestProductionIntegration::test_security_configuration": true, "test_production_integration.py::TestProductionIntegration::test_container_resource_limits": true, "test_production_integration.py::TestProductionIntegration::test_log_aggregation_setup": true, "test_project_generator_comprehensive.py::TestProjectGenerator::test_documentation_generation": true, "test_project_generator_comprehensive.py::TestProjectGenerator::test_frontend_code_generation": true, "test_project_generator_comprehensive.py::TestProjectGenerator::test_backend_code_generation": true, "test_project_generator_comprehensive.py::TestProjectGenerator::test_project_metadata_creation": true, "test_project_generator_comprehensive.py::TestProjectGenerator::test_error_handling": true, "test_project_generator_comprehensive.py::TestProjectGeneratorIntegration::test_full_project_generation_workflow": true, "test_api_manager.py": true}