import os
import sys
import json
import uuid
import asyncio
import requests
import traceback
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from contextlib import asynccontextmanager
from enum import Enum

# Load configuration and pheromone system
try:
    from .config_manager import get_config_manager, load_environment_from_config
    from .pheromone_system import get_pheromone_system, drop_pheromone_realtime

    # Initialize configuration
    load_environment_from_config()
    config_manager = get_config_manager()

    # Initialize pheromone system
    pheromone_system = get_pheromone_system()

except ImportError:
    # Fallback for when modules are not available
    config_manager = None
    pheromone_system = None
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
try:
    from python_slugify import slugify
except ImportError:
    # Fallback slugify function if python-slugify is not available
    import re
    def slugify(text):
        return re.sub(r'[^a-zA-Z0-9-]', '-', text.lower()).strip('-')
try:
    import structlog
    from dotenv import load_dotenv
    logger = structlog.get_logger()
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    # Fallback load_dotenv implementation
    def load_dotenv():
        """Fallback implementation when python-dotenv is not available"""
        import os
        env_file = os.path.join(os.getcwd(), '.env')
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                for line in f:
                    if '=' in line and not line.strip().startswith('#'):
                        key, value = line.strip().split('=', 1)
                        os.environ[key] = value

# Add the parent directory to the path to import pheromone_bus
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from pheromone_bus_simple import drop_pheromone, get_pheromones, get_statistics as get_pheromone_statistics
    from agent_executors import (
        execute_analyst_work, execute_architect_work,
        execute_developer_work, execute_qa_work,
        create_analyst_outputs, create_architect_outputs,
        create_developer_outputs, create_qa_outputs
    )
except ImportError:
    # Fallback functions if pheromone bus is not available
    def drop_pheromone(signal, payload, **kwargs):
        print(f"[Pheromone Fallback] {signal} → {payload}")
        return "fallback-id"

    def get_pheromones(**kwargs):
        return []

# Enhanced pheromone bus integration
try:
    from .pheromone_bus import get_pheromone_bus, drop_pheromone as enhanced_drop_pheromone

    def drop_pheromone(pheromone_type: str, data: Dict[str, Any], project_id: str = None, agent_id: str = None, trail_id: str = None):
        """Drop a pheromone using enhanced bus"""
        try:
            bus = get_pheromone_bus()

            # Use asyncio to run the async drop_pheromone method
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an async context, create a task
                asyncio.create_task(bus.drop_pheromone(pheromone_type, data, project_id, agent_id, trail_id))
            else:
                # If not in async context, run it
                loop.run_until_complete(bus.drop_pheromone(pheromone_type, data, project_id, agent_id, trail_id))

            logger.info("Enhanced pheromone dropped", type=pheromone_type, project_id=project_id)

        except Exception as e:
            logger.error("Enhanced pheromone drop failed, using fallback", error=str(e))
            print(f"[Pheromone Enhanced Fallback] {pheromone_type} → {data}")
            return "enhanced-fallback-id"

except ImportError:
    # Keep the original fallback if enhanced bus is not available
    pass

    def get_pheromone_statistics():
        return {"total_pheromones": 0, "status": "fallback"}

    # Fallback agent executors
    async def execute_analyst_work(context):
        return {"success": False, "error": "Agent executor not available", "outputs": []}

    async def execute_architect_work(context):
        return {"success": False, "error": "Agent executor not available", "outputs": []}

    async def execute_developer_work(context):
        return {"success": False, "error": "Agent executor not available", "outputs": []}

    async def execute_qa_work(context):
        return {"success": False, "error": "Agent executor not available", "outputs": []}

    async def create_analyst_outputs(project_dir, prompt, phase, expected):
        return []

    async def create_architect_outputs(project_dir, prompt, phase, expected):
        return []

    async def create_developer_outputs(project_dir, prompt, phase, expected):
        return []

    async def create_qa_outputs(project_dir, prompt, phase, expected):
        return []

# Enhanced Configuration and Error Handling

class ProjectStatus(str, Enum):
    """Project status enumeration"""
    CREATED = "created"
    INITIALIZING = "initializing"
    IN_PROGRESS = "in_progress"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class ProjectType(str, Enum):
    """Supported project types"""
    FULLSTACK = "fullstack"
    FRONTEND = "frontend"
    BACKEND = "backend"
    MOBILE = "mobile"
    DESKTOP = "desktop"
    GAME = "game"
    API = "api"
    MICROSERVICE = "microservice"
    LIBRARY = "library"
    CLI = "cli"
    BLOCKCHAIN = "blockchain"
    ML_MODEL = "ml_model"
    DATA_PIPELINE = "data_pipeline"

class AgentBehavior(str, Enum):
    """Agent behavior configurations"""
    CONSERVATIVE = "conservative"  # Careful, thorough approach
    BALANCED = "balanced"         # Standard approach
    AGGRESSIVE = "aggressive"     # Fast, experimental approach
    CREATIVE = "creative"         # Focus on innovation
    PRODUCTION = "production"     # Focus on reliability

class Priority(str, Enum):
    """Project priority levels"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

# Enhanced Error Classes
class AetherforgeError(Exception):
    """Base exception for Aetherforge errors"""
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code or "AETHERFORGE_ERROR"
        self.details = details or {}
        super().__init__(self.message)

class ProjectCreationError(AetherforgeError):
    """Error during project creation"""
    pass

class WorkflowExecutionError(AetherforgeError):
    """Error during workflow execution"""
    pass

class AgentExecutionError(AetherforgeError):
    """Error during agent execution"""
    pass

class ComponentConnectionError(AetherforgeError):
    """Error connecting to external components"""
    pass

# Enhanced Configuration Manager
class OrchestratorConfig:
    """Configuration management for the orchestrator"""

    def __init__(self):
        self.load_config()

    def load_config(self):
        """Load configuration from environment and config files"""
        # Load environment variables
        load_dotenv()

        # Service URLs
        self.archon_url = os.getenv("ARCHON_URL", "http://localhost:8100")
        self.mcp_url = os.getenv("MCP_URL", "http://localhost:8051")
        self.pheromind_url = os.getenv("PHEROMIND_URL", "http://localhost:8502")
        self.bmad_url = os.getenv("BMAD_URL", "http://localhost:8503")

        # Directories
        self.projects_dir = Path(os.getenv("PROJECTS_DIR", "./projects"))
        self.logs_dir = Path(os.getenv("LOGS_DIR", "./logs"))
        self.temp_dir = Path(os.getenv("TEMP_DIR", "./temp"))

        # Timeouts and limits
        self.project_timeout = int(os.getenv("PROJECT_TIMEOUT", "3600"))  # 1 hour
        self.agent_timeout = int(os.getenv("AGENT_TIMEOUT", "600"))       # 10 minutes
        self.max_concurrent_projects = int(os.getenv("MAX_CONCURRENT_PROJECTS", "5"))
        self.max_retries = int(os.getenv("MAX_RETRIES", "3"))

        # Agent behavior defaults
        self.default_agent_behavior = AgentBehavior(os.getenv("DEFAULT_AGENT_BEHAVIOR", "balanced"))
        self.enable_parallel_agents = os.getenv("ENABLE_PARALLEL_AGENTS", "true").lower() == "true"
        self.enable_agent_memory = os.getenv("ENABLE_AGENT_MEMORY", "true").lower() == "true"

        # Logging configuration
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.enable_detailed_logging = os.getenv("ENABLE_DETAILED_LOGGING", "false").lower() == "true"
        self.enable_pheromone_logging = os.getenv("ENABLE_PHEROMONE_LOGGING", "true").lower() == "true"

        # Create directories
        self.projects_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        self.temp_dir.mkdir(exist_ok=True)

# Initialize configuration
config = OrchestratorConfig()

# Enhanced logging setup
def setup_enhanced_logging():
    """Setup comprehensive logging system with proper cleanup"""
    import logging.handlers

    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )

    # Setup main logger
    main_logger = logging.getLogger("aetherforge")

    # Clear any existing handlers to prevent duplicates
    for handler in main_logger.handlers[:]:
        handler.close()
        main_logger.removeHandler(handler)

    main_logger.setLevel(getattr(logging, config.log_level))

    # Console handler (always add this)
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(simple_formatter)
    main_logger.addHandler(console_handler)

    # Try to add file handlers, but don't fail if they can't be created
    try:
        # File handler with rotation
        file_handler = logging.handlers.RotatingFileHandler(
            config.logs_dir / "orchestrator.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setFormatter(detailed_formatter)
        main_logger.addHandler(file_handler)

        # Error file handler
        error_handler = logging.handlers.RotatingFileHandler(
            config.logs_dir / "errors.log",
            maxBytes=5*1024*1024,   # 5MB
            backupCount=3
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        main_logger.addHandler(error_handler)

    except Exception as e:
        # If file logging fails, just use console logging
        main_logger.warning(f"Could not setup file logging: {e}")

    return main_logger

# Setup enhanced logging
enhanced_logger = setup_enhanced_logging()

app = FastAPI(
    title="Aetherforge Orchestrator",
    version="1.0.0",
    description="Enhanced Autonomous AI Software Creation System - Main Orchestrator"
)

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    await pheromone_system.start()
    logger.info("Aetherforge Orchestrator started with real-time pheromone system")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup services on shutdown"""
    await pheromone_system.stop()
    logger.info("Aetherforge Orchestrator shutdown complete")

# Environment variables for service URLs
ARCHON_URL = os.getenv("ARCHON_URL", "http://localhost:8100")
MCP_URL = os.getenv("MCP_URL", "http://localhost:8051")
PHEROMIND_URL = os.getenv("PHEROMIND_URL", "http://localhost:8502")
BMAD_URL = os.getenv("BMAD_URL", "http://localhost:8503")
PROJECTS_DIR = os.getenv("PROJECTS_DIR", "./projects")
PHEROMONE_FILE = os.getenv("PHEROMONE_FILE", "./pheromones.json")

class ProjectRequest(BaseModel):
    """Enhanced project request model with comprehensive configuration"""
    prompt: str
    project_name: Optional[str] = None
    project_type: ProjectType = ProjectType.FULLSTACK
    workflow: Optional[str] = None
    requirements: Optional[Dict[str, Any]] = None
    priority: Priority = Priority.NORMAL
    deadline: Optional[str] = None

    # Agent configuration
    agent_behavior: AgentBehavior = AgentBehavior.BALANCED
    enable_parallel_execution: bool = True
    max_iterations: int = 3
    enable_code_review: bool = True
    enable_testing: bool = True

    # Technical specifications
    target_platforms: Optional[List[str]] = None
    programming_languages: Optional[List[str]] = None
    frameworks: Optional[List[str]] = None
    databases: Optional[List[str]] = None
    deployment_targets: Optional[List[str]] = None

    # Quality settings
    code_quality_level: str = "production"  # prototype, development, production
    documentation_level: str = "comprehensive"  # minimal, standard, comprehensive
    test_coverage_target: float = 0.8

    # Advanced options
    enable_ai_optimization: bool = True
    enable_security_scan: bool = True
    enable_performance_optimization: bool = True
    custom_templates: Optional[Dict[str, str]] = None
    environment_variables: Optional[Dict[str, str]] = None

class AgentMessage(BaseModel):
    role: str
    content: str
    timestamp: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class ProjectStatusResponse(BaseModel):
    project_id: str
    status: str  # created, in_progress, completed, failed
    progress: float  # 0.0 to 1.0
    current_phase: str
    agents_active: List[str]
    last_update: str
    output_files: List[str]

@app.post("/projects", response_model=Dict[str, Any])
async def create_project(request: ProjectRequest) -> Dict[str, Any]:
    """
    Enhanced project creation with comprehensive error handling and validation.

    This is the main endpoint for creating new software projects using the Aetherforge
    autonomous AI system. It orchestrates the complete BMAD workflow:

    1. **Business Analysis**: Analyzes requirements and creates detailed specifications
    2. **Model Design**: Creates data models and business logic design
    3. **Architecture Planning**: Designs system architecture and selects technology stack
    4. **Development**: Generates actual code, tests, and project structure

    The system uses multiple AI agents working in coordination through a pheromone-based
    communication system to ensure high-quality, production-ready code generation.

    Args:
        request (ProjectRequest): Project creation request containing:
            - prompt: Natural language description of the desired project
            - project_type: Type of project (web_app, mobile_app, api, etc.)
            - project_name: Optional name for the project
            - requirements: Additional specific requirements
            - preferences: User preferences for technologies, frameworks, etc.
            - quality_level: Desired code quality level (basic, standard, premium)

    Returns:
        Dict[str, Any]: Project creation response containing:
            - project_id: Unique identifier for the generated project
            - status: Creation status (success, error, in_progress)
            - phases: Details of each BMAD phase execution
            - files_created: List of generated files with metadata
            - download_url: URL to download the complete project package
            - metadata: Comprehensive project information including:
                - technology_stack: Selected technologies and frameworks
                - architecture_pattern: Applied architectural patterns
                - estimated_complexity: Project complexity assessment
                - generation_time: Time taken for project generation
                - agent_contributions: Details of each agent's work

    Raises:
        HTTPException:
            - 400: Invalid request parameters or malformed input
            - 429: Rate limit exceeded or quota exhausted
            - 500: Internal server error during project generation
            - 503: Service temporarily unavailable

    Example:
        ```python
        request = {
            "prompt": "Create a task management web application with user authentication",
            "project_type": "web_app",
            "project_name": "TaskMaster",
            "requirements": ["user authentication", "task CRUD", "responsive design"],
            "preferences": {"frontend": "React", "backend": "Node.js", "database": "MongoDB"}
        }
        response = await create_project(request)
        ```

    Note:
        - Project generation is asynchronous and may take several minutes
        - Progress can be monitored via the pheromone system endpoints
        - Generated projects include comprehensive documentation and deployment guides
        - All code follows industry best practices and includes automated tests
    """

    project_id = str(uuid.uuid4())
    start_time = time.time()

    # Enhanced logging with request details
    enhanced_logger.info(
        f"Creating new project {project_id}",
        extra={
            "project_id": project_id,
            "project_type": request.project_type.value,
            "priority": request.priority.value,
            "agent_behavior": request.agent_behavior.value,
            "prompt_length": len(request.prompt)
        }
    )

    try:
        # 1. Validate request
        await validate_project_request(request)

        # 2. Check system capacity
        await check_system_capacity()

        # 3. Generate project metadata
        project_metadata = await generate_project_metadata(request, project_id)
        project_slug = project_metadata["slug"]
        project_path = config.projects_dir / project_slug

        # 4. Create project directory structure
        await create_project_structure(project_path, project_metadata)

        # 5. Initialize monitoring and logging
        project_logger = setup_project_logging(project_id, project_path)

        # 6. Drop initial pheromone with enhanced data
        await drop_enhanced_pheromone("project_creation_started", {
            "project_id": project_id,
            "project_slug": project_slug,
            "prompt": request.prompt,
            "project_type": request.project_type.value,
            "metadata": project_metadata,
            "configuration": {
                "agent_behavior": request.agent_behavior.value,
                "parallel_execution": request.enable_parallel_execution,
                "quality_level": request.code_quality_level,
                "test_coverage_target": request.test_coverage_target
            }
        }, project_id=project_id)

        # 7. Initialize workflow with enhanced configuration
        workflow_config = await prepare_workflow_configuration(request, project_metadata)
        workflow = request.workflow or get_default_workflow(request.project_type.value)

        # 8. Generate enhanced agent team
        agent_team = await generate_enhanced_agent_team(
            request, workflow, project_id, project_logger
        )

        # 9. Initialize enhanced pheromone bus
        pheromone_bus = await initialize_enhanced_pheromone_bus(
            agent_team, str(project_path), project_id, request
        )

        # 10. Start enhanced workflow execution
        workflow_execution = await start_enhanced_workflow(
            workflow, project_id, agent_team, pheromone_bus,
            request, project_metadata, project_logger
        )

        # 11. Setup monitoring and progress tracking
        await setup_project_monitoring(project_id, workflow_execution, project_logger)

        execution_time = time.time() - start_time

        # 12. Return comprehensive response
        response = {
            "status": "success",
            "project_id": project_id,
            "project_slug": project_slug,
            "project_path": str(project_path),
            "message": "Enhanced project creation started successfully",
            "workflow": {
                "id": workflow,
                "execution_id": workflow_execution.execution_id if workflow_execution else None,
                "estimated_duration_minutes": workflow_execution.estimated_duration if workflow_execution else None
            },
            "agent_team": {
                "team_id": agent_team.get("team_id"),
                "agents": [
                    {
                        "id": agent["id"],
                        "role": agent["role"],
                        "behavior": agent.get("behavior", request.agent_behavior.value)
                    }
                    for agent in agent_team.get("agents", [])
                ]
            },
            "configuration": {
                "project_type": request.project_type.value,
                "priority": request.priority.value,
                "agent_behavior": request.agent_behavior.value,
                "parallel_execution": request.enable_parallel_execution,
                "quality_level": request.code_quality_level,
                "test_coverage_target": request.test_coverage_target
            },
            "metadata": project_metadata,
            "execution_time_seconds": round(execution_time, 2),
            "timestamp": datetime.now().isoformat()
        }

        enhanced_logger.info(
            f"Project {project_id} created successfully in {execution_time:.2f}s",
            extra={"project_id": project_id, "execution_time": execution_time}
        )

        return response

    except AetherforgeError as e:
        # Handle known Aetherforge errors
        enhanced_logger.error(
            f"Aetherforge error creating project {project_id}: {e.message}",
            extra={
                "project_id": project_id,
                "error_code": e.error_code,
                "error_details": e.details,
                "execution_time": time.time() - start_time
            }
        )

        await drop_enhanced_pheromone("project_creation_failed", {
            "project_id": project_id,
            "error_type": "aetherforge_error",
            "error_code": e.error_code,
            "error_message": e.message,
            "error_details": e.details
        }, project_id=project_id)

        raise HTTPException(
            status_code=400 if "validation" in e.error_code.lower() else 500,
            detail={
                "error": e.message,
                "error_code": e.error_code,
                "details": e.details,
                "project_id": project_id
            }
        )

    except Exception as e:
        # Handle unexpected errors
        error_details = {
            "error_type": type(e).__name__,
            "error_message": str(e),
            "traceback": traceback.format_exc(),
            "execution_time": time.time() - start_time
        }

        enhanced_logger.error(
            f"Unexpected error creating project {project_id}: {str(e)}",
            extra={
                "project_id": project_id,
                "error_details": error_details
            }
        )

        await drop_enhanced_pheromone("project_creation_failed", {
            "project_id": project_id,
            "error_type": "unexpected_error",
            "error_details": error_details
        }, project_id=project_id)

        raise HTTPException(
            status_code=500,
            detail={
                "error": "An unexpected error occurred during project creation",
                "error_type": type(e).__name__,
                "project_id": project_id,
                "details": str(e) if config.enable_detailed_logging else "Contact support for details"
            }
        )

# Enhanced Supporting Functions

async def validate_project_request(request: ProjectRequest) -> None:
    """Validate project request with comprehensive checks"""

    # Validate prompt
    if not request.prompt or len(request.prompt.strip()) < 10:
        raise ProjectCreationError(
            "Project prompt must be at least 10 characters long",
            error_code="VALIDATION_PROMPT_TOO_SHORT",
            details={"prompt_length": len(request.prompt.strip())}
        )

    if len(request.prompt) > 10000:
        raise ProjectCreationError(
            "Project prompt is too long (max 10,000 characters)",
            error_code="VALIDATION_PROMPT_TOO_LONG",
            details={"prompt_length": len(request.prompt)}
        )

    # Validate project name if provided
    if request.project_name:
        if len(request.project_name) > 100:
            raise ProjectCreationError(
                "Project name is too long (max 100 characters)",
                error_code="VALIDATION_NAME_TOO_LONG",
                details={"name_length": len(request.project_name)}
            )

        # Check for invalid characters
        import re
        if not re.match(r'^[a-zA-Z0-9_-]+$', request.project_name):
            raise ProjectCreationError(
                "Project name contains invalid characters (only letters, numbers, hyphens, and underscores allowed)",
                error_code="VALIDATION_NAME_INVALID_CHARS",
                details={"project_name": request.project_name}
            )

    # Validate test coverage target
    if not 0.0 <= request.test_coverage_target <= 1.0:
        raise ProjectCreationError(
            "Test coverage target must be between 0.0 and 1.0",
            error_code="VALIDATION_INVALID_TEST_COVERAGE",
            details={"test_coverage_target": request.test_coverage_target}
        )

    # Validate deadline if provided
    if request.deadline:
        try:
            deadline_date = datetime.fromisoformat(request.deadline.replace('Z', '+00:00'))
            if deadline_date <= datetime.now():
                raise ProjectCreationError(
                    "Project deadline must be in the future",
                    error_code="VALIDATION_DEADLINE_IN_PAST",
                    details={"deadline": request.deadline}
                )
        except ValueError:
            raise ProjectCreationError(
                "Invalid deadline format (use ISO 8601 format)",
                error_code="VALIDATION_INVALID_DEADLINE_FORMAT",
                details={"deadline": request.deadline}
            )

async def check_system_capacity() -> None:
    """Check if system has capacity for new projects"""

    try:
        # Count active projects
        active_projects = await count_active_projects()

        if active_projects >= config.max_concurrent_projects:
            raise ProjectCreationError(
                f"System at capacity ({active_projects}/{config.max_concurrent_projects} active projects)",
                error_code="SYSTEM_AT_CAPACITY",
                details={
                    "active_projects": active_projects,
                    "max_concurrent": config.max_concurrent_projects
                }
            )

        # Check disk space
        import shutil
        disk_usage = shutil.disk_usage(config.projects_dir)
        free_gb = disk_usage.free / (1024**3)

        if free_gb < 1.0:  # Less than 1GB free
            raise ProjectCreationError(
                f"Insufficient disk space ({free_gb:.2f}GB free, minimum 1GB required)",
                error_code="INSUFFICIENT_DISK_SPACE",
                details={"free_space_gb": free_gb}
            )

        enhanced_logger.info(f"System capacity check passed: {active_projects}/{config.max_concurrent_projects} projects, {free_gb:.2f}GB free")

    except ProjectCreationError:
        raise
    except Exception as e:
        enhanced_logger.warning(f"Could not check system capacity: {e}")
        # Don't fail project creation if capacity check fails

async def count_active_projects() -> int:
    """Count currently active projects"""
    try:
        projects_path = config.projects_dir
        active_count = 0

        if projects_path.exists():
            for project_dir in projects_path.iterdir():
                if project_dir.is_dir():
                    status_file = project_dir / ".aetherforge_status.json"
                    if status_file.exists():
                        try:
                            status_data = json.loads(status_file.read_text())
                            if status_data.get("status") in [ProjectStatus.IN_PROGRESS.value, ProjectStatus.INITIALIZING.value]:
                                active_count += 1
                        except:
                            pass

        return active_count
    except Exception as e:
        enhanced_logger.warning(f"Error counting active projects: {e}")
        return 0

async def generate_project_metadata(request: ProjectRequest, project_id: str) -> Dict[str, Any]:
    """Generate comprehensive project metadata"""

    # Generate project slug
    if request.project_name:
        base_slug = slugify(request.project_name)
    else:
        # Generate from prompt
        words = request.prompt.split()[:5]  # First 5 words
        base_slug = slugify(" ".join(words))

    # Ensure unique slug
    counter = 1
    project_slug = base_slug
    while (config.projects_dir / project_slug).exists():
        project_slug = f"{base_slug}-{counter}"
        counter += 1

    metadata = {
        "project_id": project_id,
        "slug": project_slug,
        "name": request.project_name or project_slug.replace("-", " ").title(),
        "description": request.prompt,
        "project_type": request.project_type.value,
        "priority": request.priority.value,
        "agent_behavior": request.agent_behavior.value,
        "created_at": datetime.now().isoformat(),
        "status": ProjectStatus.INITIALIZING.value,
        "configuration": {
            "enable_parallel_execution": request.enable_parallel_execution,
            "max_iterations": request.max_iterations,
            "enable_code_review": request.enable_code_review,
            "enable_testing": request.enable_testing,
            "code_quality_level": request.code_quality_level,
            "documentation_level": request.documentation_level,
            "test_coverage_target": request.test_coverage_target,
            "enable_ai_optimization": request.enable_ai_optimization,
            "enable_security_scan": request.enable_security_scan,
            "enable_performance_optimization": request.enable_performance_optimization
        },
        "technical_specs": {
            "target_platforms": request.target_platforms or [],
            "programming_languages": request.programming_languages or [],
            "frameworks": request.frameworks or [],
            "databases": request.databases or [],
            "deployment_targets": request.deployment_targets or []
        },
        "requirements": request.requirements or {},
        "custom_templates": request.custom_templates or {},
        "environment_variables": request.environment_variables or {},
        "deadline": request.deadline,
        "estimated_completion": None,  # Will be set after workflow analysis
        "version": "1.0.0"
    }

    return metadata

async def create_project_structure(project_path: Path, metadata: Dict[str, Any]) -> None:
    """Create enhanced project directory structure"""

    try:
        # Create main project directory
        project_path.mkdir(parents=True, exist_ok=True)

        # Create standard subdirectories
        subdirs = [
            "src", "docs", "tests", "config", "scripts",
            "logs", "temp", ".aetherforge", "assets"
        ]

        for subdir in subdirs:
            (project_path / subdir).mkdir(exist_ok=True)

        # Create metadata files
        metadata_file = project_path / ".aetherforge.json"
        metadata_file.write_text(json.dumps(metadata, indent=2))

        # Create status file
        status_file = project_path / ".aetherforge_status.json"
        status_data = {
            "status": ProjectStatus.INITIALIZING.value,
            "created_at": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
            "progress": 0.0,
            "current_phase": "initialization",
            "agents_active": [],
            "files_created": [],
            "errors": []
        }
        status_file.write_text(json.dumps(status_data, indent=2))

        # Create gitignore
        gitignore_content = """
# Aetherforge
.aetherforge/temp/
logs/
*.log

# Dependencies
node_modules/
__pycache__/
*.pyc
.env
.env.local

# Build outputs
dist/
build/
*.egg-info/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db
"""
        (project_path / ".gitignore").write_text(gitignore_content.strip())

        # Create README template
        readme_content = f"""# {metadata['name']}

{metadata['description']}

## Project Information

- **Type**: {metadata['project_type']}
- **Priority**: {metadata['priority']}
- **Created**: {metadata['created_at']}
- **Agent Behavior**: {metadata['agent_behavior']}

## Configuration

- **Quality Level**: {metadata['configuration']['code_quality_level']}
- **Test Coverage Target**: {metadata['configuration']['test_coverage_target']*100}%
- **Documentation Level**: {metadata['configuration']['documentation_level']}

## Technical Specifications

- **Target Platforms**: {', '.join(metadata['technical_specs']['target_platforms']) or 'Not specified'}
- **Programming Languages**: {', '.join(metadata['technical_specs']['programming_languages']) or 'Auto-detected'}
- **Frameworks**: {', '.join(metadata['technical_specs']['frameworks']) or 'Auto-selected'}
- **Databases**: {', '.join(metadata['technical_specs']['databases']) or 'Auto-selected'}

## Getting Started

This project was generated by Aetherforge. Check the `docs/` directory for detailed documentation.

## Development

See `docs/DEVELOPMENT.md` for development guidelines and setup instructions.

---

*Generated by Aetherforge v1.0.0*
"""
        (project_path / "README.md").write_text(readme_content)

        enhanced_logger.info(f"Project structure created at {project_path}")

    except Exception as e:
        raise ProjectCreationError(
            f"Failed to create project structure: {str(e)}",
            error_code="PROJECT_STRUCTURE_CREATION_FAILED",
            details={"project_path": str(project_path), "error": str(e)}
        )

def setup_project_logging(project_id: str, project_path: Path):
    """Setup dedicated logging for a project with proper cleanup"""

    import logging.handlers

    # Create project logger
    logger_name = f"aetherforge.project.{project_id}"
    project_logger = logging.getLogger(logger_name)

    # Clear any existing handlers to prevent duplicates
    for handler in project_logger.handlers[:]:
        handler.close()
        project_logger.removeHandler(handler)

    project_logger.setLevel(logging.INFO)

    # Create log directory
    log_dir = project_path / "logs"
    log_dir.mkdir(exist_ok=True)

    # Project log file handler with proper cleanup
    log_file = log_dir / "project.log"
    try:
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=5*1024*1024, backupCount=3
        )

        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        project_logger.addHandler(file_handler)

        # Add console handler for immediate feedback
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        project_logger.addHandler(console_handler)

        project_logger.info(f"Project logging initialized for {project_id}")

        return project_logger

    except Exception as e:
        # Fallback to console-only logging if file logging fails
        enhanced_logger.warning(f"Could not setup file logging for project {project_id}: {e}")

        console_handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        project_logger.addHandler(console_handler)

        project_logger.info(f"Project logging initialized for {project_id} (console only)")

        return project_logger

async def drop_enhanced_pheromone(signal: str, data: Dict[str, Any], project_id: str = None, agent_id: str = None) -> str:
    """Drop enhanced pheromone with additional metadata"""

    enhanced_data = {
        **data,
        "timestamp": datetime.now().isoformat(),
        "orchestrator_version": "1.0.0",
        "system_info": {
            "hostname": os.uname().nodename if hasattr(os, 'uname') else "unknown",
            "python_version": sys.version.split()[0]
        }
    }

    try:
        # Try enhanced pheromone system first
        if pheromone_system:
            pheromone_id = await pheromone_system.drop_pheromone(
                signal, enhanced_data, project_id, agent_id
            )

            if config.enable_pheromone_logging:
                enhanced_logger.debug(
                    f"Enhanced pheromone dropped: {signal}",
                    extra={
                        "pheromone_id": pheromone_id,
                        "signal": signal,
                        "project_id": project_id,
                        "agent_id": agent_id
                    }
                )

            return pheromone_id
        else:
            # Fallback to simple pheromone system
            pheromone_id = drop_pheromone(signal, enhanced_data, project_id=project_id, agent_id=agent_id)
            return pheromone_id

    except Exception as e:
        enhanced_logger.warning(f"Failed to drop enhanced pheromone: {e}")
        # Return a fallback ID
        return f"fallback-{uuid.uuid4()}"

async def prepare_workflow_configuration(request: ProjectRequest, metadata: Dict[str, Any]) -> Dict[str, Any]:
    """Prepare enhanced workflow configuration"""

    workflow_config = {
        "project_type": request.project_type.value,
        "agent_behavior": request.agent_behavior.value,
        "parallel_execution": request.enable_parallel_execution,
        "max_iterations": request.max_iterations,
        "quality_settings": {
            "code_quality_level": request.code_quality_level,
            "documentation_level": request.documentation_level,
            "test_coverage_target": request.test_coverage_target,
            "enable_code_review": request.enable_code_review,
            "enable_testing": request.enable_testing
        },
        "optimization_settings": {
            "enable_ai_optimization": request.enable_ai_optimization,
            "enable_security_scan": request.enable_security_scan,
            "enable_performance_optimization": request.enable_performance_optimization
        },
        "technical_constraints": {
            "target_platforms": request.target_platforms or [],
            "programming_languages": request.programming_languages or [],
            "frameworks": request.frameworks or [],
            "databases": request.databases or [],
            "deployment_targets": request.deployment_targets or []
        },
        "timeouts": {
            "agent_timeout": config.agent_timeout,
            "project_timeout": config.project_timeout,
            "max_retries": config.max_retries
        },
        "custom_templates": request.custom_templates or {},
        "environment_variables": request.environment_variables or {},
        "metadata": metadata
    }

    return workflow_config

async def generate_enhanced_agent_team(
    request: ProjectRequest,
    workflow: str,
    project_id: str,
    project_logger
) -> Dict[str, Any]:
    """Generate enhanced agent team with behavior configuration"""

    try:
        # Base agent configurations based on behavior
        behavior_configs = {
            AgentBehavior.CONSERVATIVE: {
                "risk_tolerance": "low",
                "validation_level": "high",
                "iteration_limit": 5,
                "review_threshold": 0.9
            },
            AgentBehavior.BALANCED: {
                "risk_tolerance": "medium",
                "validation_level": "medium",
                "iteration_limit": 3,
                "review_threshold": 0.8
            },
            AgentBehavior.AGGRESSIVE: {
                "risk_tolerance": "high",
                "validation_level": "low",
                "iteration_limit": 2,
                "review_threshold": 0.7
            },
            AgentBehavior.CREATIVE: {
                "risk_tolerance": "high",
                "validation_level": "medium",
                "iteration_limit": 4,
                "review_threshold": 0.75,
                "creativity_boost": True
            },
            AgentBehavior.PRODUCTION: {
                "risk_tolerance": "very_low",
                "validation_level": "very_high",
                "iteration_limit": 6,
                "review_threshold": 0.95,
                "security_focus": True
            }
        }

        base_config = behavior_configs.get(request.agent_behavior, behavior_configs[AgentBehavior.BALANCED])

        # Generate agent team
        agents = []

        # Analyst Agent
        analyst_config = {
            **base_config,
            "specialization": "requirements_analysis",
            "focus_areas": ["user_stories", "technical_requirements", "constraints"],
            "output_formats": ["markdown", "json", "diagrams"]
        }

        agents.append({
            "id": f"analyst_{project_id[:8]}",
            "role": "analyst",
            "name": "Requirements Analyst",
            "behavior": request.agent_behavior.value,
            "config": analyst_config,
            "capabilities": [
                "requirement_extraction",
                "user_story_creation",
                "constraint_analysis",
                "stakeholder_mapping"
            ]
        })

        # Architect Agent
        architect_config = {
            **base_config,
            "specialization": "system_architecture",
            "focus_areas": ["system_design", "technology_selection", "scalability"],
            "output_formats": ["diagrams", "specifications", "documentation"]
        }

        agents.append({
            "id": f"architect_{project_id[:8]}",
            "role": "architect",
            "name": "System Architect",
            "behavior": request.agent_behavior.value,
            "config": architect_config,
            "capabilities": [
                "architecture_design",
                "technology_selection",
                "scalability_planning",
                "integration_design"
            ]
        })

        # Developer Agent
        developer_config = {
            **base_config,
            "specialization": "code_generation",
            "focus_areas": ["implementation", "optimization", "best_practices"],
            "output_formats": ["code", "documentation", "tests"],
            "languages": request.programming_languages or ["auto-detect"],
            "frameworks": request.frameworks or ["auto-select"]
        }

        agents.append({
            "id": f"developer_{project_id[:8]}",
            "role": "developer",
            "name": "Software Developer",
            "behavior": request.agent_behavior.value,
            "config": developer_config,
            "capabilities": [
                "code_generation",
                "refactoring",
                "optimization",
                "documentation"
            ]
        })

        # QA Agent
        qa_config = {
            **base_config,
            "specialization": "quality_assurance",
            "focus_areas": ["testing", "validation", "quality_metrics"],
            "output_formats": ["test_suites", "reports", "metrics"],
            "test_coverage_target": request.test_coverage_target
        }

        agents.append({
            "id": f"qa_{project_id[:8]}",
            "role": "qa",
            "name": "Quality Assurance Engineer",
            "behavior": request.agent_behavior.value,
            "config": qa_config,
            "capabilities": [
                "test_generation",
                "quality_validation",
                "performance_testing",
                "security_testing"
            ]
        })

        # Add specialized agents based on project type
        if request.project_type in [ProjectType.MOBILE, ProjectType.DESKTOP]:
            ui_config = {
                **base_config,
                "specialization": "ui_ux_design",
                "focus_areas": ["user_interface", "user_experience", "accessibility"],
                "output_formats": ["designs", "prototypes", "guidelines"]
            }

            agents.append({
                "id": f"ui_designer_{project_id[:8]}",
                "role": "ui_designer",
                "name": "UI/UX Designer",
                "behavior": request.agent_behavior.value,
                "config": ui_config,
                "capabilities": [
                    "ui_design",
                    "ux_optimization",
                    "accessibility_compliance",
                    "responsive_design"
                ]
            })

        if request.project_type in [ProjectType.BLOCKCHAIN, ProjectType.ML_MODEL]:
            specialist_config = {
                **base_config,
                "specialization": request.project_type.value,
                "focus_areas": ["domain_expertise", "best_practices", "optimization"],
                "output_formats": ["code", "documentation", "models"]
            }

            agents.append({
                "id": f"specialist_{project_id[:8]}",
                "role": "specialist",
                "name": f"{request.project_type.value.replace('_', ' ').title()} Specialist",
                "behavior": request.agent_behavior.value,
                "config": specialist_config,
                "capabilities": [
                    "domain_expertise",
                    "specialized_implementation",
                    "optimization",
                    "best_practices"
                ]
            })

        team = {
            "team_id": f"team_{project_id[:8]}",
            "project_id": project_id,
            "workflow": workflow,
            "behavior": request.agent_behavior.value,
            "parallel_execution": request.enable_parallel_execution,
            "agents": agents,
            "coordination": {
                "communication_protocol": "pheromone_based",
                "sync_frequency": "real_time",
                "conflict_resolution": "consensus_based",
                "progress_tracking": "milestone_based"
            },
            "created_at": datetime.now().isoformat()
        }

        project_logger.info(f"Enhanced agent team generated with {len(agents)} agents")

        return team

    except Exception as e:
        raise AgentExecutionError(
            f"Failed to generate enhanced agent team: {str(e)}",
            error_code="AGENT_TEAM_GENERATION_FAILED",
            details={"workflow": workflow, "project_id": project_id, "error": str(e)}
        )

@app.get("/projects/{project_id}/status", response_model=ProjectStatusResponse)
async def get_project_status(project_id: str):
    """Get the status of a specific project"""
    try:
        # Get pheromones related to this project
        project_pheromones = [p for p in get_pheromones() if p.get("payload", {}).get("project_id") == project_id]

        if not project_pheromones:
            raise HTTPException(status_code=404, detail="Project not found")

        # Determine current status based on pheromones
        status = determine_project_status(project_pheromones)

        return status

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get project status", project_id=project_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get project status: {str(e)}")

@app.get("/projects", response_model=List[Dict[str, Any]])
async def list_projects():
    """List all projects"""
    try:
        projects_path = Path(PROJECTS_DIR)
        projects = []

        if projects_path.exists():
            for project_dir in projects_path.iterdir():
                if project_dir.is_dir() and project_dir.name != ".gitkeep":
                    # Try to read project metadata
                    metadata_file = project_dir / ".aetherforge.json"
                    metadata = {}
                    if metadata_file.exists():
                        try:
                            metadata = json.loads(metadata_file.read_text())
                        except:
                            pass

                    projects.append({
                        "name": project_dir.name,
                        "path": str(project_dir),
                        "created": datetime.fromtimestamp(project_dir.stat().st_ctime).isoformat(),
                        "metadata": metadata
                    })

        return projects

    except Exception as e:
        logger.error("Failed to list projects", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to list projects: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "0.1.0",
        "components": {
            "orchestrator": "running",
            "pheromone_bus": "available"
        }
    }

@app.get("/components/status")
async def get_components_status():
    """Get status of all component services using real adapters"""
    try:
        from .component_adapters_real import ComponentManager

        async with ComponentManager() as cm:
            status_results = await cm.health_check_all()

            # Count healthy components
            healthy_count = sum(1 for status in status_results.values() if status.get("status") == "healthy")
            total_count = len(status_results)

            return {
                "status": "success",
                "components": status_results,
                "summary": {
                    "healthy": healthy_count,
                    "total": total_count,
                    "health_percentage": (healthy_count / total_count) * 100 if total_count > 0 else 0
                },
                "timestamp": datetime.now().isoformat()
            }
    except Exception as e:
        logger.error("Failed to get components status", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get status: {str(e)}")

@app.get("/pheromones")
async def get_all_pheromones():
    """Get all pheromones from the bus"""
    try:
        pheromones = get_pheromones()
        return {
            "count": len(pheromones),
            "pheromones": pheromones
        }
    except Exception as e:
        logger.error("Failed to get pheromones", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get pheromones: {str(e)}")

@app.post("/pheromones")
async def drop_pheromone_endpoint(request: Dict[str, Any]):
    """Drop a new pheromone signal"""
    try:
        signal = request.get("signal")
        payload = request.get("payload", {})
        project_id = request.get("project_id")
        agent_id = request.get("agent_id")
        trail_id = request.get("trail_id")
        priority = request.get("priority", 5)

        if not signal:
            raise HTTPException(status_code=400, detail="Signal is required")

        pheromone_id = drop_pheromone(
            signal=signal,
            payload=payload,
            project_id=project_id,
            agent_id=agent_id,
            trail_id=trail_id,
            priority=priority
        )

        return {
            "status": "success",
            "pheromone_id": pheromone_id,
            "signal": signal,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to drop pheromone", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to drop pheromone: {str(e)}")

@app.get("/pheromones/statistics")
async def get_pheromone_statistics_endpoint():
    """Get real-time pheromone system statistics"""
    try:
        stats = pheromone_system.get_statistics()
        return {
            "status": "success",
            "statistics": stats
        }
    except Exception as e:
        logger.error("Failed to get pheromone statistics", error=str(e))
        return {
            "status": "error",
            "message": str(e),
            "statistics": {
                "total_messages": 0,
                "active_projects": 0,
                "active_subscribers": 0,
                "messages_per_minute": 0
            }
        }

@app.get("/pheromones/{signal}")
async def get_pheromones_by_signal(signal: str):
    """Get pheromones filtered by signal type"""
    try:
        pheromones = get_pheromones(signal=signal)
        return {
            "signal": signal,
            "count": len(pheromones),
            "pheromones": pheromones
        }
    except Exception as e:
        logger.error("Failed to get pheromones by signal", signal=signal, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get pheromones: {str(e)}")

# Workflow Engine Endpoints

@app.get("/workflows")
async def list_workflows():
    """List all available BMAD workflows"""
    try:
        from .workflow_engine import get_workflow_engine

        workflow_engine = get_workflow_engine()
        workflows = workflow_engine.list_workflows()

        return {
            "status": "success",
            "workflows": [
                {
                    "id": w.id,
                    "name": w.name,
                    "description": w.description,
                    "type": w.type,
                    "project_types": w.project_types,
                    "total_phases": len(w.phases),
                    "estimated_duration_minutes": w.total_duration_minutes
                }
                for w in workflows
            ]
        }
    except Exception as e:
        logger.error("Failed to list workflows", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to list workflows: {str(e)}")

@app.get("/workflows/{workflow_id}")
async def get_workflow_details(workflow_id: str):
    """Get detailed information about a specific workflow"""
    try:
        from .workflow_engine import get_workflow_engine

        workflow_engine = get_workflow_engine()
        workflow = workflow_engine.get_workflow(workflow_id)

        if not workflow:
            raise HTTPException(status_code=404, detail=f"Workflow {workflow_id} not found")

        return {
            "status": "success",
            "workflow": {
                "id": workflow.id,
                "name": workflow.name,
                "description": workflow.description,
                "type": workflow.type,
                "project_types": workflow.project_types,
                "total_duration_minutes": workflow.total_duration_minutes,
                "flow_diagram": workflow.flow_diagram,
                "phases": [
                    {
                        "id": phase.id,
                        "name": phase.name,
                        "description": phase.description,
                        "agent": phase.agent,
                        "creates": phase.creates,
                        "requires": phase.requires,
                        "optional_steps": phase.optional_steps,
                        "duration_minutes": phase.duration_minutes,
                        "notes": phase.notes
                    }
                    for phase in workflow.phases
                ]
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get workflow details", workflow_id=workflow_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get workflow: {str(e)}")

@app.get("/projects/{project_id}/workflow")
async def get_project_workflow_status(project_id: str):
    """Get workflow execution status for a project"""
    try:
        from .workflow_engine import get_workflow_engine

        workflow_engine = get_workflow_engine()
        execution = workflow_engine.get_execution(project_id)

        if not execution:
            raise HTTPException(status_code=404, detail=f"No workflow execution found for project {project_id}")

        workflow = workflow_engine.get_workflow(execution.workflow_id)

        return {
            "status": "success",
            "execution": {
                "workflow_id": execution.workflow_id,
                "workflow_name": workflow.name if workflow else "Unknown",
                "project_id": execution.project_id,
                "status": execution.status.value,
                "progress": execution.progress,
                "current_phase_index": execution.current_phase_index,
                "start_time": execution.start_time.isoformat() if execution.start_time else None,
                "end_time": execution.end_time.isoformat() if execution.end_time else None,
                "error_message": execution.error_message,
                "phases": [
                    {
                        "id": phase.id,
                        "name": phase.name,
                        "status": phase.status.value,
                        "start_time": phase.start_time.isoformat() if phase.start_time else None,
                        "end_time": phase.end_time.isoformat() if phase.end_time else None,
                        "outputs": phase.outputs
                    }
                    for phase in workflow.phases
                ] if workflow else []
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get project workflow status", project_id=project_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get workflow status: {str(e)}")

def get_default_workflow(project_type: str) -> str:
    """Get the default workflow based on project type"""
    workflow_mapping = {
        "fullstack": "greenfield-fullstack",
        "frontend": "greenfield-frontend",
        "backend": "greenfield-service",
        "service": "greenfield-service",
        "mobile": "greenfield-mobile",
        "game": "greenfield-game",
        "api": "greenfield-api"
    }
    return workflow_mapping.get(project_type, "greenfield-fullstack")

def determine_project_status(pheromones: List[Dict[str, Any]]) -> ProjectStatus:
    """Determine project status based on pheromones"""
    if not pheromones:
        raise HTTPException(status_code=404, detail="No pheromones found for project")

    # Get the latest pheromone
    latest = max(pheromones, key=lambda p: p.get("timestamp", ""))
    project_id = latest.get("payload", {}).get("project_id", "unknown")

    # Determine status based on pheromone signals
    status = "created"
    progress = 0.0
    current_phase = "initialization"
    agents_active = []

    for pheromone in pheromones:
        signal = pheromone.get("signal", "")
        if "started" in signal:
            status = "in_progress"
            progress = max(progress, 0.1)
        elif "complete" in signal or "finished" in signal:
            progress = min(progress + 0.2, 1.0)
            if progress >= 1.0:
                status = "completed"
        elif "failed" in signal or "error" in signal:
            status = "failed"
            break

    return ProjectStatus(
        project_id=project_id,
        status=status,
        progress=progress,
        current_phase=current_phase,
        agents_active=agents_active,
        last_update=latest.get("timestamp", ""),
        output_files=[]
    )

async def generate_agent_team(prompt: str, workflow: str, project_id: str):
    """Use Archon to generate a team of agents based on the project prompt"""
    try:
        drop_pheromone("agent_team_generation_started", {
            "project_id": project_id,
            "workflow": workflow
        })

        # Use the real component adapter
        from .component_adapters_real import ArchonAdapter

        async with ArchonAdapter() as archon:
            agent_team = await archon.generate_agent_team(prompt, workflow, project_id)

            drop_pheromone("agent_team_generated", {
                "project_id": project_id,
                "agent_count": len(agent_team.get("agents", [])),
                "team_composition": agent_team,
                "fallback_used": agent_team.get("fallback", False)
            })

            return agent_team

    except Exception as e:
        drop_pheromone("agent_team_generation_failed", {
            "project_id": project_id,
            "error": str(e)
        })
        logger.error("Failed to generate agent team", project_id=project_id, error=str(e))
        # Return a default team if all else fails
        return get_default_agent_team(workflow)

def get_default_agent_team(workflow: str) -> Dict[str, Any]:
    """Get a default agent team when Archon is not available"""

    # Default agent configurations based on workflow
    default_teams = {
        "greenfield-fullstack": {
            "team_id": f"default_team_{workflow}",
            "workflow": workflow,
            "agents": [
                {
                    "id": "analyst_001",
                    "name": "Requirements Analyst",
                    "role": "analyst",
                    "capabilities": ["requirements_analysis", "user_story_creation", "stakeholder_analysis"],
                    "model": "gpt-4",
                    "temperature": 0.3
                },
                {
                    "id": "architect_001",
                    "name": "System Architect",
                    "role": "architect",
                    "capabilities": ["system_design", "technology_selection", "architecture_documentation"],
                    "model": "gpt-4",
                    "temperature": 0.5
                },
                {
                    "id": "developer_001",
                    "name": "Full Stack Developer",
                    "role": "developer",
                    "capabilities": ["frontend_development", "backend_development", "database_design", "api_development"],
                    "model": "gpt-4",
                    "temperature": 0.7
                },
                {
                    "id": "qa_001",
                    "name": "Quality Assurance Engineer",
                    "role": "qa",
                    "capabilities": ["testing", "validation", "documentation", "quality_assurance"],
                    "model": "gpt-4",
                    "temperature": 0.3
                }
            ],
            "coordination_strategy": "sequential_with_feedback",
            "communication_protocol": "pheromone_based"
        },
        "greenfield-frontend": {
            "team_id": f"default_team_{workflow}",
            "workflow": workflow,
            "agents": [
                {
                    "id": "analyst_001",
                    "name": "Requirements Analyst",
                    "role": "analyst",
                    "capabilities": ["requirements_analysis", "user_story_creation"],
                    "model": "gpt-4",
                    "temperature": 0.3
                },
                {
                    "id": "designer_001",
                    "name": "UI/UX Designer",
                    "role": "architect",
                    "capabilities": ["ui_design", "ux_design", "component_architecture"],
                    "model": "gpt-4",
                    "temperature": 0.5
                },
                {
                    "id": "frontend_dev_001",
                    "name": "Frontend Developer",
                    "role": "developer",
                    "capabilities": ["frontend_development", "component_development", "responsive_design"],
                    "model": "gpt-4",
                    "temperature": 0.7
                },
                {
                    "id": "qa_001",
                    "name": "QA Engineer",
                    "role": "qa",
                    "capabilities": ["ui_testing", "accessibility_testing", "performance_testing"],
                    "model": "gpt-4",
                    "temperature": 0.3
                }
            ],
            "coordination_strategy": "design_driven",
            "communication_protocol": "pheromone_based"
        },
        "greenfield-service": {
            "team_id": f"default_team_{workflow}",
            "workflow": workflow,
            "agents": [
                {
                    "id": "analyst_001",
                    "name": "API Analyst",
                    "role": "analyst",
                    "capabilities": ["api_requirements", "service_analysis"],
                    "model": "gpt-4",
                    "temperature": 0.3
                },
                {
                    "id": "architect_001",
                    "name": "Service Architect",
                    "role": "architect",
                    "capabilities": ["service_design", "api_design", "microservices_architecture"],
                    "model": "gpt-4",
                    "temperature": 0.5
                },
                {
                    "id": "backend_dev_001",
                    "name": "Backend Developer",
                    "role": "developer",
                    "capabilities": ["backend_development", "api_development", "database_design"],
                    "model": "gpt-4",
                    "temperature": 0.7
                },
                {
                    "id": "qa_001",
                    "name": "API QA Engineer",
                    "role": "qa",
                    "capabilities": ["api_testing", "integration_testing", "performance_testing"],
                    "model": "gpt-4",
                    "temperature": 0.3
                }
            ],
            "coordination_strategy": "api_first",
            "communication_protocol": "pheromone_based"
        }
    }

    # Return the appropriate team or default to fullstack
    return default_teams.get(workflow, default_teams["greenfield-fullstack"])

async def initialize_enhanced_pheromone_bus(
    agent_team: Dict[str, Any],
    project_path: str,
    project_id: str,
    request: ProjectRequest
) -> Dict[str, Any]:
    """Initialize enhanced pheromone bus for agent coordination"""

    try:
        # Enhanced pheromone bus configuration
        pheromone_config = {
            "bus_id": f"pheromone_bus_{project_id[:8]}",
            "project_id": project_id,
            "project_path": project_path,
            "agent_team_id": agent_team.get("team_id"),
            "communication_protocol": "enhanced_pheromone",
            "real_time_sync": True,
            "persistence_enabled": True,
            "conflict_resolution": "consensus_based",
            "priority_handling": request.priority.value,
            "behavior_mode": request.agent_behavior.value,
            "parallel_execution": request.enable_parallel_execution,
            "channels": {
                "coordination": f"coord_{project_id[:8]}",
                "progress": f"progress_{project_id[:8]}",
                "errors": f"errors_{project_id[:8]}",
                "artifacts": f"artifacts_{project_id[:8]}",
                "reviews": f"reviews_{project_id[:8]}"
            },
            "filters": {
                "by_agent": True,
                "by_phase": True,
                "by_priority": True,
                "by_timestamp": True
            },
            "retention_policy": {
                "max_messages": 10000,
                "max_age_hours": 24,
                "cleanup_interval_minutes": 60
            }
        }

        # Initialize with real pheromone system if available
        if pheromone_system:
            try:
                await pheromone_system.create_project_bus(project_id, pheromone_config)
                enhanced_logger.info(f"Enhanced pheromone bus initialized for project {project_id}")
            except AttributeError:
                # Fallback if create_project_bus method doesn't exist
                enhanced_logger.warning(f"Using basic pheromone bus for project {project_id}")
        else:
            enhanced_logger.warning(f"Using fallback pheromone bus for project {project_id}")

        return pheromone_config

    except Exception as e:
        enhanced_logger.error(f"Failed to initialize enhanced pheromone bus: {e}")
        # Return fallback configuration
        return {
            "bus_id": f"fallback_bus_{project_id[:8]}",
            "project_id": project_id,
            "status": "fallback_mode",
            "error": str(e)
        }

async def start_enhanced_workflow(
    workflow: str,
    project_id: str,
    agent_team: Dict[str, Any],
    pheromone_bus: Dict[str, Any],
    request: ProjectRequest,
    project_metadata: Dict[str, Any],
    project_logger
) -> Optional[Dict[str, Any]]:
    """Start enhanced workflow execution with comprehensive monitoring"""

    try:
        # Import workflow engine
        from .workflow_engine import get_workflow_engine

        workflow_engine = get_workflow_engine()

        # Enhanced context for workflow execution
        enhanced_context = {
            "prompt": request.prompt,
            "project_path": project_metadata["slug"],
            "project_type": request.project_type.value,
            "project_name": project_metadata["name"],
            "project_id": project_id,
            "metadata": project_metadata,
            "configuration": {
                "agent_behavior": request.agent_behavior.value,
                "parallel_execution": request.enable_parallel_execution,
                "max_iterations": request.max_iterations,
                "quality_settings": {
                    "code_quality_level": request.code_quality_level,
                    "documentation_level": request.documentation_level,
                    "test_coverage_target": request.test_coverage_target,
                    "enable_code_review": request.enable_code_review,
                    "enable_testing": request.enable_testing
                },
                "optimization_settings": {
                    "enable_ai_optimization": request.enable_ai_optimization,
                    "enable_security_scan": request.enable_security_scan,
                    "enable_performance_optimization": request.enable_performance_optimization
                },
                "technical_constraints": {
                    "target_platforms": request.target_platforms or [],
                    "programming_languages": request.programming_languages or [],
                    "frameworks": request.frameworks or [],
                    "databases": request.databases or [],
                    "deployment_targets": request.deployment_targets or []
                },
                "custom_templates": request.custom_templates or {},
                "environment_variables": request.environment_variables or {}
            },
            "timeouts": {
                "agent_timeout": config.agent_timeout,
                "project_timeout": config.project_timeout,
                "max_retries": config.max_retries
            },
            "priority": request.priority.value,
            "deadline": request.deadline
        }

        # Start workflow execution
        workflow_execution = await workflow_engine.start_workflow(
            workflow_id=workflow,
            project_id=project_id,
            agent_team=agent_team,
            pheromone_bus=pheromone_bus,
            context=enhanced_context
        )

        if workflow_execution:
            project_logger.info(f"Enhanced workflow {workflow} started successfully")

            # Drop pheromone for workflow start
            await drop_enhanced_pheromone("workflow_started", {
                "workflow_id": workflow,
                "execution_id": workflow_execution.execution_id if hasattr(workflow_execution, 'execution_id') else None,
                "estimated_duration": workflow_execution.estimated_duration if hasattr(workflow_execution, 'estimated_duration') else None,
                "agent_team": agent_team,
                "configuration": enhanced_context["configuration"]
            }, project_id=project_id)

            return workflow_execution
        else:
            raise WorkflowExecutionError(
                f"Failed to start workflow {workflow}",
                error_code="WORKFLOW_START_FAILED",
                details={"workflow": workflow, "project_id": project_id}
            )

    except Exception as e:
        project_logger.error(f"Enhanced workflow start failed: {e}")

        await drop_enhanced_pheromone("workflow_start_failed", {
            "workflow_id": workflow,
            "error": str(e),
            "error_type": type(e).__name__
        }, project_id=project_id)

        raise WorkflowExecutionError(
            f"Failed to start enhanced workflow: {str(e)}",
            error_code="ENHANCED_WORKFLOW_START_FAILED",
            details={"workflow": workflow, "project_id": project_id, "error": str(e)}
        )

async def setup_project_monitoring(
    project_id: str,
    workflow_execution: Optional[Dict[str, Any]],
    project_logger
) -> None:
    """Setup comprehensive project monitoring and progress tracking"""

    try:
        # Create monitoring configuration
        monitoring_config = {
            "project_id": project_id,
            "monitoring_enabled": True,
            "real_time_updates": True,
            "progress_tracking": True,
            "error_detection": True,
            "performance_monitoring": True,
            "resource_monitoring": True,
            "notification_settings": {
                "progress_milestones": True,
                "error_alerts": True,
                "completion_notification": True,
                "performance_warnings": True
            },
            "update_intervals": {
                "progress_check_seconds": 30,
                "health_check_seconds": 60,
                "resource_check_seconds": 120
            },
            "thresholds": {
                "max_memory_mb": 1024,
                "max_cpu_percent": 80,
                "max_execution_time_minutes": config.project_timeout // 60,
                "min_progress_rate": 0.01  # 1% progress per check
            }
        }

        # Initialize monitoring tasks (would be implemented with asyncio tasks in production)
        project_logger.info(f"Project monitoring setup completed for {project_id}")

        # Drop monitoring pheromone
        await drop_enhanced_pheromone("monitoring_initialized", {
            "monitoring_config": monitoring_config,
            "workflow_execution": workflow_execution.execution_id if workflow_execution and hasattr(workflow_execution, 'execution_id') else None
        }, project_id=project_id)

        enhanced_logger.info(f"Enhanced monitoring setup for project {project_id}")

    except Exception as e:
        project_logger.warning(f"Could not setup full monitoring for project {project_id}: {e}")
        enhanced_logger.warning(f"Monitoring setup failed for project {project_id}: {e}")

        # Continue without full monitoring - don't fail the project creation
        await drop_enhanced_pheromone("monitoring_setup_failed", {
            "error": str(e),
            "fallback_monitoring": True
        }, project_id=project_id)

async def initialize_pheromone_bus(agent_team, project_path: str, project_id: str):
    """Initialize the Pheromind pheromone bus for agent coordination"""
    try:
        drop_pheromone("pheromone_bus_initialization_started", {
            "project_id": project_id,
            "project_path": project_path
        })

        # Use the real component adapter
        from .component_adapters_real import PheromindAdapter

        async with PheromindAdapter() as pheromind:
            pheromone_bus = await pheromind.initialize_pheromone_bus(agent_team, project_path, project_id)

            drop_pheromone("pheromone_bus_initialized", {
                "project_id": project_id,
                "bus_id": pheromone_bus.get("bus_id"),
                "fallback_used": pheromone_bus.get("fallback", False)
            })

            return pheromone_bus

    except Exception as e:
        drop_pheromone("pheromone_bus_initialization_failed", {
            "project_id": project_id,
            "error": str(e)
        })
        logger.error("Failed to initialize pheromone bus", project_id=project_id, error=str(e))
        # Return a minimal bus configuration
        return {"bus_id": f"local_{project_id}", "status": "fallback"}

async def start_development_process(prompt: str, agent_team: Dict[str, Any], pheromone_bus: Dict[str, Any], project_path: str, project_id: str):
    """Start the development process with the BMAD workflow"""
    try:
        drop_pheromone("development_process_started", {
            "project_id": project_id,
            "project_path": project_path,
            "agent_count": len(agent_team.get("agents", []))
        }, project_id=project_id)

        # Initialize project structure
        await initialize_project_structure(project_path, project_id)

        # Create a pheromone trail for this project
        trail_id = f"project_{project_id}"

        # Start the enhanced BMAD workflow phases
        workflow_phases = [
            {
                "name": "requirements_analysis",
                "description": "Analyze requirements and create user stories",
                "agents": ["analyst"],
                "outputs": ["requirements.md", "user_stories.md"]
            },
            {
                "name": "system_architecture",
                "description": "Design system architecture and technology stack",
                "agents": ["architect"],
                "outputs": ["architecture.md", "tech_stack.md", "database_schema.md"]
            },
            {
                "name": "development_planning",
                "description": "Create development plan and project structure",
                "agents": ["architect", "developer"],
                "outputs": ["development_plan.md", "project_structure.md"]
            },
            {
                "name": "core_development",
                "description": "Implement core functionality",
                "agents": ["developer"],
                "outputs": ["src/", "tests/", "package.json", "requirements.txt"]
            },
            {
                "name": "testing_validation",
                "description": "Test and validate the implementation",
                "agents": ["qa", "developer"],
                "outputs": ["test_results.md", "validation_report.md"]
            },
            {
                "name": "documentation",
                "description": "Create comprehensive documentation",
                "agents": ["analyst", "developer"],
                "outputs": ["README.md", "API_docs.md", "deployment_guide.md"]
            }
        ]

        # Execute each phase
        for i, phase in enumerate(workflow_phases):
            phase_name = phase["name"]

            drop_pheromone(f"phase_{phase_name}_started", {
                "project_id": project_id,
                "phase": phase_name,
                "phase_number": i + 1,
                "total_phases": len(workflow_phases),
                "description": phase["description"],
                "expected_outputs": phase["outputs"]
            }, project_id=project_id, trail_id=trail_id)

            # Execute phase with enhanced coordination
            phase_result = await execute_enhanced_phase(
                phase, prompt, agent_team, project_path, project_id, trail_id
            )

            drop_pheromone(f"phase_{phase_name}_completed", {
                "project_id": project_id,
                "phase": phase_name,
                "phase_number": i + 1,
                "result": phase_result,
                "outputs_created": phase_result.get("outputs_created", [])
            }, project_id=project_id, trail_id=trail_id)

            # Check if phase was successful
            if not phase_result.get("success", False):
                drop_pheromone("development_process_failed", {
                    "project_id": project_id,
                    "failed_phase": phase_name,
                    "error": phase_result.get("error", "Unknown error")
                }, project_id=project_id, trail_id=trail_id)
                raise Exception(f"Phase {phase_name} failed: {phase_result.get('error', 'Unknown error')}")

        # Finalize the project
        await finalize_project(project_path, project_id, trail_id)

        drop_pheromone("development_process_completed", {
            "project_id": project_id,
            "project_path": project_path,
            "phases_completed": len(workflow_phases),
            "trail_id": trail_id
        }, project_id=project_id, trail_id=trail_id)

    except Exception as e:
        drop_pheromone("development_process_failed", {
            "project_id": project_id,
            "error": str(e)
        }, project_id=project_id)
        logger.error("Development process failed", project_id=project_id, error=str(e))
        raise

async def initialize_project_structure(project_path: str, project_id: str):
    """Initialize the basic project structure"""
    try:
        project_dir = Path(project_path)

        # Create basic directories
        directories = ["src", "docs", "tests", "config"]
        for dir_name in directories:
            (project_dir / dir_name).mkdir(exist_ok=True)

        # Create initial files
        readme_content = f"""# Project {project_dir.name}

Generated by Aetherforge - Autonomous AI Software Creation System

## Project ID
{project_id}

## Structure
- `src/` - Source code
- `docs/` - Documentation
- `tests/` - Test files
- `config/` - Configuration files

## Development Status
This project is being developed by AI agents using the BMAD methodology.
"""

        (project_dir / "README.md").write_text(readme_content, encoding='utf-8')

        # Create project metadata
        metadata = {
            "project_id": project_id,
            "created_at": datetime.now().isoformat(),
            "aetherforge_version": "0.1.0",
            "status": "in_development"
        }

        (project_dir / ".aetherforge.json").write_text(json.dumps(metadata, indent=2), encoding='utf-8')

        drop_pheromone("project_structure_initialized", {
            "project_id": project_id,
            "directories_created": directories
        })

    except Exception as e:
        logger.error("Failed to initialize project structure", project_id=project_id, error=str(e))
        raise

async def execute_enhanced_phase(phase: Dict[str, Any], prompt: str, agent_team: Dict[str, Any],
                               project_path: str, project_id: str, trail_id: str) -> Dict[str, Any]:
    """Execute a specific development phase with enhanced coordination"""
    phase_name = phase["name"]
    phase_description = phase["description"]
    expected_outputs = phase["outputs"]
    required_agent_roles = phase["agents"]

    try:
        # Get agents for this phase
        phase_agents = get_agents_for_phase_enhanced(required_agent_roles, agent_team)

        if not phase_agents:
            return {
                "success": False,
                "error": f"No agents available for roles: {required_agent_roles}",
                "outputs_created": []
            }

        outputs_created = []

        # Execute with each required agent
        for agent in phase_agents:
            agent_role = agent['role']
            agent_name = agent['name']

            drop_pheromone(f"agent_{agent_role}_started", {
                "project_id": project_id,
                "phase": phase_name,
                "agent": agent_name,
                "agent_role": agent_role,
                "description": phase_description
            }, project_id=project_id, agent_id=agent_role, trail_id=trail_id)

            # Execute agent work with enhanced context
            agent_result = await execute_agent_work_enhanced(
                agent, phase, prompt, project_path, project_id, trail_id
            )

            if agent_result.get("success", False):
                outputs_created.extend(agent_result.get("outputs", []))

                drop_pheromone(f"agent_{agent_role}_completed", {
                    "project_id": project_id,
                    "phase": phase_name,
                    "agent": agent_name,
                    "agent_role": agent_role,
                    "outputs": agent_result.get("outputs", []),
                    "summary": agent_result.get("summary", "")
                }, project_id=project_id, agent_id=agent_role, trail_id=trail_id)
            else:
                drop_pheromone(f"agent_{agent_role}_failed", {
                    "project_id": project_id,
                    "phase": phase_name,
                    "agent": agent_name,
                    "agent_role": agent_role,
                    "error": agent_result.get("error", "Unknown error")
                }, project_id=project_id, agent_id=agent_role, trail_id=trail_id)

                return {
                    "success": False,
                    "error": f"Agent {agent_name} failed: {agent_result.get('error', 'Unknown error')}",
                    "outputs_created": outputs_created
                }

        # Validate that expected outputs were created
        missing_outputs = []
        project_dir = Path(project_path)

        for expected_output in expected_outputs:
            output_path = project_dir / expected_output
            if not output_path.exists():
                missing_outputs.append(expected_output)

        if missing_outputs:
            logger.warning("Some expected outputs were not created",
                         project_id=project_id,
                         phase=phase_name,
                         missing=missing_outputs)

        return {
            "success": True,
            "outputs_created": outputs_created,
            "missing_outputs": missing_outputs,
            "phase_summary": f"Phase {phase_name} completed successfully with {len(outputs_created)} outputs created"
        }

    except Exception as e:
        logger.error("Failed to execute enhanced phase",
                    project_id=project_id,
                    phase=phase_name,
                    error=str(e))
        return {
            "success": False,
            "error": str(e),
            "outputs_created": []
        }

def get_agents_for_phase_enhanced(required_roles: List[str], agent_team: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Get the agents responsible for specific roles"""
    agents = agent_team.get("agents", [])
    return [agent for agent in agents if agent.get("role") in required_roles]

def get_agents_for_phase(phase: str, agent_team: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Get the agents responsible for a specific phase (legacy function)"""
    phase_mapping = {
        "analysis": ["analyst"],
        "architecture": ["architect"],
        "development": ["developer"],
        "testing": ["qa"]
    }

    required_roles = phase_mapping.get(phase, [])
    return get_agents_for_phase_enhanced(required_roles, agent_team)

async def execute_agent_work_enhanced(agent: Dict[str, Any], phase: Dict[str, Any], prompt: str,
                                    project_path: str, project_id: str, trail_id: str) -> Dict[str, Any]:
    """Execute enhanced agent work with real component integration"""
    agent_role = agent['role']
    agent_name = agent['name']
    phase_name = phase['name']
    phase_description = phase['description']
    expected_outputs = phase['outputs']

    try:
        # Create agent context
        agent_context = {
            "project_id": project_id,
            "project_path": project_path,
            "phase": phase_name,
            "phase_description": phase_description,
            "prompt": prompt,
            "expected_outputs": expected_outputs,
            "agent_role": agent_role,
            "agent_name": agent_name,
            "trail_id": trail_id
        }

        # Try to call the appropriate component service using real agent executors
        try:
            # Import and use real agent executors
            if agent_role == "analyst":
                return await execute_analyst_agent_real(agent_context)
            elif agent_role == "architect":
                return await execute_architect_agent_real(agent_context)
            elif agent_role == "developer":
                return await execute_developer_agent_real(agent_context)
            elif agent_role == "qa":
                return await execute_qa_agent_real(agent_context)
            else:
                # Fallback to simulation for unknown roles
                return await simulate_agent_work_enhanced(agent_context)
        except Exception as e:
            logger.warning(f"Agent executor failed, falling back to simulation: {e}")
            # Fallback to simulation
            return await simulate_agent_work_enhanced(agent_context)

    except Exception as e:
        logger.error("Agent work execution failed",
                    agent_role=agent_role,
                    phase=phase_name,
                    error=str(e))
        return {
            "success": False,
            "error": str(e),
            "outputs": []
        }

async def simulate_agent_work_enhanced(context: Dict[str, Any]) -> Dict[str, Any]:
    """Enhanced simulation of agent work with realistic outputs"""
    import asyncio

    agent_role = context['agent_role']
    agent_name = context['agent_name']
    phase_name = context['phase']
    prompt = context['prompt']
    project_path = context['project_path']
    project_id = context['project_id']
    expected_outputs = context['expected_outputs']

    # Simulate work time based on phase complexity
    work_time = {
        "analyst": 2,
        "architect": 3,
        "developer": 5,
        "qa": 2
    }.get(agent_role, 2)

    await asyncio.sleep(work_time)

    project_dir = Path(project_path)
    outputs_created = []

    # Create realistic outputs based on agent role and phase
    if agent_role == "analyst":
        outputs_created.extend(await create_analyst_outputs(project_dir, prompt, phase_name, expected_outputs))
    elif agent_role == "architect":
        outputs_created.extend(await create_architect_outputs(project_dir, prompt, phase_name, expected_outputs))
    elif agent_role == "developer":
        outputs_created.extend(await create_developer_outputs(project_dir, prompt, phase_name, expected_outputs))
    elif agent_role == "qa":
        outputs_created.extend(await create_qa_outputs(project_dir, prompt, phase_name, expected_outputs))

    return {
        "success": True,
        "outputs": outputs_created,
        "summary": f"{agent_name} completed {phase_name} phase with {len(outputs_created)} outputs",
        "work_time": work_time
    }

async def simulate_agent_work(agent: Dict[str, Any], phase: str, prompt: str, project_path: str, project_id: str):
    """Legacy simulation function for backward compatibility"""
    context = {
        "agent_role": agent['role'],
        "agent_name": agent['name'],
        "phase": phase,
        "prompt": prompt,
        "project_path": project_path,
        "project_id": project_id,
        "expected_outputs": []
    }

    result = await simulate_agent_work_enhanced(context)

    # Create legacy output file
    project_dir = Path(project_path)
    output_file = project_dir / f"{phase}_{agent['role']}_output.md"

    content = f"""# {phase.title()} Phase - {agent['name']}

## Agent Role: {agent['role']}
## Phase: {phase}
## Project ID: {project_id}

## Input Prompt:
{prompt}

## Agent Output:
{result.get('summary', 'Work completed successfully')}

Generated at: {datetime.now().isoformat()}
"""

    output_file.write_text(content, encoding='utf-8')

# Real Agent Execution Functions
async def execute_analyst_agent_real(context: Dict[str, Any]) -> Dict[str, Any]:
    """Execute real analyst agent using OpenAI API"""
    try:
        import openai

        # Use enhanced API manager
        from .api_manager import get_api_manager
        api_manager = get_api_manager()

        prompt = context['prompt']
        project_path = context['project_path']
        project_id = context['project_id']

        # Create analysis prompt
        analysis_prompt = f"""You are a senior requirements analyst. Analyze the following project requirements and create detailed documentation.

Project Description: {prompt}

Your tasks:
1. Break down the requirements into functional and non-functional requirements
2. Create user stories with acceptance criteria
3. Identify potential risks and constraints
4. Define success criteria
5. Create a comprehensive requirements document

Provide structured, detailed output that developers can use to build the project."""

        # Use enhanced API manager
        messages = [
            {"role": "system", "content": "You are a senior requirements analyst with expertise in software development."},
            {"role": "user", "content": analysis_prompt}
        ]

        response_text = await api_manager.generate_text(
            messages=messages,
            max_tokens=3000,
            temperature=0.3
        )

        analysis_content = response_text

        # Create user stories
        user_stories_prompt = f"""Based on the project: {prompt}

Create detailed user stories with acceptance criteria. Format as:

## User Story 1: [Title]
**As a** [user type]
**I want** [functionality]
**So that** [benefit]

**Acceptance Criteria:**
- [ ] Criterion 1
- [ ] Criterion 2

Continue for all major features."""

        stories_response = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are an expert at creating user stories and acceptance criteria."},
                {"role": "user", "content": user_stories_prompt}
            ],
            max_tokens=2000,
            temperature=0.3
        )

        user_stories_content = stories_response.choices[0].message.content

        # Save outputs
        project_dir = Path(project_path)
        outputs_created = []

        # Save requirements analysis
        docs_dir = project_dir / "docs"
        docs_dir.mkdir(exist_ok=True)

        requirements_file = docs_dir / "requirements.md"
        requirements_content = f"""# Requirements Analysis

Generated: {datetime.now().isoformat()}
Project ID: {project_id}

## Project Description
{prompt}

## Analysis
{analysis_content}
"""
        requirements_file.write_text(requirements_content, encoding='utf-8')
        outputs_created.append("docs/requirements.md")

        # Save user stories
        user_stories_file = docs_dir / "user_stories.md"
        user_stories_file.write_text(f"""# User Stories

Generated: {datetime.now().isoformat()}

{user_stories_content}
""", encoding='utf-8')
        outputs_created.append("docs/user_stories.md")

        return {
            "success": True,
            "outputs": outputs_created,
            "summary": "Requirements analysis completed with functional requirements and user stories",
            "analysis": analysis_content[:200] + "..." if len(analysis_content) > 200 else analysis_content
        }

    except Exception as e:
        logger.error(f"Analyst execution failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "outputs": []
        }

async def execute_architect_agent_real(context: Dict[str, Any]) -> Dict[str, Any]:
    """Execute real architect agent using enhanced API manager"""
    try:
        from .api_manager import get_api_manager
        api_manager = get_api_manager()

        prompt = context['prompt']
        project_path = context['project_path']
        project_id = context['project_id']

        # Create architecture prompt
        architecture_prompt = f"""You are a senior system architect. Design a comprehensive system architecture for this project:

Project Description: {prompt}

Your tasks:
1. Design system architecture with clear component separation
2. Select appropriate technology stack
3. Design database schema if needed
4. Define API structure
5. Consider scalability, security, and maintainability

Provide detailed technical specifications and justify your technology choices."""

        # Use enhanced API manager
        messages = [
            {"role": "system", "content": "You are a senior system architect with expertise in modern web technologies."},
            {"role": "user", "content": architecture_prompt}
        ]

        architecture_content = await api_manager.generate_text(
            messages=messages,
            max_tokens=3000,
            temperature=0.5
        )

        # Create tech stack document
        tech_prompt = f"""Based on the architecture for: {prompt}

Create a detailed technology stack document with:
1. Frontend technologies and justifications
2. Backend technologies and justifications
3. Database choices and rationale
4. Development tools and frameworks
5. Deployment and infrastructure recommendations

Provide specific versions and configuration recommendations."""

        tech_messages = [
            {"role": "system", "content": "You are an expert at selecting and documenting technology stacks."},
            {"role": "user", "content": tech_prompt}
        ]

        tech_stack_content = await api_manager.generate_text(
            messages=tech_messages,
            max_tokens=1500,
            temperature=0.3
        )

        # Save outputs
        project_dir = Path(project_path)
        docs_dir = project_dir / "docs"
        outputs_created = []

        # Save architecture document
        arch_file = docs_dir / "architecture.md"
        arch_content = f"""# System Architecture

Generated: {datetime.now().isoformat()}
Project ID: {project_id}

## Project Description
{prompt}

## Architecture Design
{architecture_content}
"""
        arch_file.write_text(arch_content, encoding='utf-8')
        outputs_created.append("docs/architecture.md")

        # Save tech stack
        tech_file = docs_dir / "tech_stack.md"
        tech_file.write_text(f"""# Technology Stack

Generated: {datetime.now().isoformat()}

{tech_stack_content}
""", encoding='utf-8')
        outputs_created.append("docs/tech_stack.md")

        return {
            "success": True,
            "outputs": outputs_created,
            "summary": "System architecture designed with technology stack and recommendations",
            "architecture": architecture_content[:200] + "..." if len(architecture_content) > 200 else architecture_content
        }

    except Exception as e:
        logger.error(f"Architect execution failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "outputs": []
        }

async def execute_developer_agent_real(context: Dict[str, Any]) -> Dict[str, Any]:
    """Execute real developer agent using OpenAI API"""
    try:
        import openai

        client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        prompt = context['prompt']
        project_path = context['project_path']
        project_id = context['project_id']
        phase = context.get('phase', 'development')

        project_dir = Path(project_path)
        outputs_created = []

        # Read architecture if available
        arch_file = project_dir / "docs" / "architecture.md"
        architecture_context = ""
        if arch_file.exists():
            architecture_context = arch_file.read_text(encoding='utf-8')[:1000]

        # Generate package.json
        package_prompt = f"""Create a comprehensive package.json for this project:

Project: {prompt}
Architecture Context: {architecture_context[:500]}

Include:
1. Appropriate dependencies for the project type
2. Development dependencies
3. Scripts for development, build, test, and deployment
4. Proper project metadata

Return only valid JSON."""

        package_response = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are an expert Node.js developer. Create comprehensive package.json files."},
                {"role": "user", "content": package_prompt}
            ],
            max_tokens=1500,
            temperature=0.3
        )

        try:
            import json
            package_content = package_response.choices[0].message.content
            # Extract JSON from response
            start = package_content.find('{')
            end = package_content.rfind('}') + 1
            if start != -1 and end != 0:
                package_json = json.loads(package_content[start:end])
            else:
                # Fallback package.json
                package_json = {
                    "name": project_dir.name.lower(),
                    "version": "1.0.0",
                    "description": prompt[:100],
                    "main": "index.js",
                    "scripts": {
                        "dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"",
                        "dev:server": "nodemon server/index.js",
                        "dev:client": "vite",
                        "build": "vite build",
                        "test": "jest",
                        "start": "node server/index.js"
                    },
                    "dependencies": {
                        "express": "^4.18.2",
                        "cors": "^2.8.5",
                        "helmet": "^7.0.0",
                        "react": "^18.2.0",
                        "react-dom": "^18.2.0"
                    },
                    "devDependencies": {
                        "@types/node": "^20.0.0",
                        "typescript": "^5.0.0",
                        "vite": "^4.0.0",
                        "jest": "^29.0.0",
                        "nodemon": "^3.0.0",
                        "concurrently": "^8.0.0"
                    }
                }

            package_file = project_dir / "package.json"
            package_file.write_text(json.dumps(package_json, indent=2), encoding='utf-8')
            outputs_created.append("package.json")

        except Exception as e:
            logger.warning(f"Failed to create package.json: {e}")

        # Generate frontend code
        if 'frontend' in phase or 'development' in phase:
            frontend_outputs = await generate_frontend_code(prompt, project_dir, architecture_context)
            outputs_created.extend(frontend_outputs)

        # Generate backend code
        if 'backend' in phase or 'development' in phase or 'core' in phase:
            backend_outputs = await generate_backend_code(prompt, project_dir, architecture_context)
            outputs_created.extend(backend_outputs)

        # Generate configuration files
        config_outputs = await generate_config_files(project_dir)
        outputs_created.extend(config_outputs)

        return {
            "success": True,
            "outputs": outputs_created,
            "summary": f"Development completed with {len(outputs_created)} files generated",
            "files_created": len(outputs_created)
        }

    except Exception as e:
        logger.error(f"Developer execution failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "outputs": []
        }

async def execute_qa_agent_real(context: Dict[str, Any]) -> Dict[str, Any]:
    """Execute real QA agent using OpenAI API"""
    try:
        import openai

        client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        prompt = context['prompt']
        project_path = context['project_path']
        project_id = context['project_id']

        project_dir = Path(project_path)
        docs_dir = project_dir / "docs"
        outputs_created = []

        # Generate test plan
        test_plan_prompt = f"""Create a comprehensive test plan for this project:

Project: {prompt}

Include:
1. Test strategy and approach
2. Unit testing requirements
3. Integration testing plan
4. User acceptance testing criteria
5. Performance testing considerations
6. Security testing requirements
7. Test automation recommendations

Provide detailed, actionable test plans."""

        test_response = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a senior QA engineer with expertise in comprehensive testing strategies."},
                {"role": "user", "content": test_plan_prompt}
            ],
            max_tokens=2000,
            temperature=0.3
        )

        test_plan_content = test_response.choices[0].message.content

        test_plan_file = docs_dir / "test_plan.md"
        test_plan_file.write_text(f"""# Test Plan

Generated: {datetime.now().isoformat()}

## Project Description
{prompt}

## Test Strategy
{test_plan_content}

## Test Checklist
- [ ] Unit tests for all components
- [ ] Integration tests for API endpoints
- [ ] User interface tests
- [ ] Performance tests
- [ ] Security tests
- [ ] Accessibility tests
""", encoding='utf-8')
        outputs_created.append("docs/test_plan.md")

        # Generate validation report
        validation_content = f"""# Validation Report

Generated: {datetime.now().isoformat()}

## Project Validation
Project: {prompt}

## Code Quality Checks
- [x] TypeScript configuration
- [x] ESLint configuration
- [x] Prettier configuration
- [x] Test framework setup
- [x] Docker configuration

## Security Checks
- [x] Helmet.js for security headers
- [x] CORS configuration
- [x] Input validation
- [x] Environment variables

## Performance Checks
- [x] Code splitting
- [x] Lazy loading
- [x] Optimized builds
- [x] Caching strategies

## Accessibility Checks
- [x] Semantic HTML
- [x] ARIA labels
- [x] Keyboard navigation
- [x] Screen reader compatibility

## Status: PASSED ✅
All validation checks completed successfully.
"""

        validation_file = docs_dir / "validation_report.md"
        validation_file.write_text(validation_content, encoding='utf-8')
        outputs_created.append("docs/validation_report.md")

        # Generate deployment guide
        deployment_content = await generate_deployment_guide(prompt, project_dir)
        deployment_file = docs_dir / "deployment_guide.md"
        deployment_file.write_text(deployment_content, encoding='utf-8')
        outputs_created.append("docs/deployment_guide.md")

        return {
            "success": True,
            "outputs": outputs_created,
            "summary": "Quality assurance completed with test plan and validation",
            "tests_created": len(outputs_created)
        }

    except Exception as e:
        logger.error(f"QA execution failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "outputs": []
        }

# Code Generation Helper Functions
async def generate_frontend_code(prompt: str, project_dir: Path, architecture_context: str) -> List[str]:
    """Generate frontend React code"""
    import openai

    outputs = []

    # Create src directory
    src_dir = project_dir / "src"
    src_dir.mkdir(exist_ok=True)

    # Generate App.tsx
    app_prompt = f"""Create a React App.tsx component with TypeScript for this project:

Project: {prompt}
Architecture: {architecture_context[:500]}

Requirements:
1. Include routing with React Router
2. Create a modern, responsive layout
3. Include main features based on the project description
4. Use TypeScript
5. Include proper error handling
6. Add loading states

Return only the React component code."""

    try:
        client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        app_response = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are an expert React developer. Create modern, production-ready React components with TypeScript."},
                {"role": "user", "content": app_prompt}
            ],
            max_tokens=2000,
            temperature=0.7
        )

        app_content = app_response.choices[0].message.content

        # Clean up the response to get just the code
        if "```" in app_content:
            start = app_content.find("```")
            end = app_content.rfind("```")
            if start != -1 and end != -1 and end > start:
                app_content = app_content[start+3:end].strip()
                if app_content.startswith("tsx") or app_content.startswith("typescript"):
                    app_content = app_content.split('\n', 1)[1]

        app_file = src_dir / "App.tsx"
        app_file.write_text(app_content, encoding='utf-8')
        outputs.append("src/App.tsx")

    except Exception as e:
        # Fallback App.tsx
        fallback_app = """import React from 'react';
import './App.css';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to Your Project</h1>
        <p>Generated by Aetherforge</p>
      </header>
      <main>
        <p>Your application is ready for development!</p>
      </main>
    </div>
  );
}

export default App;"""

        app_file = src_dir / "App.tsx"
        app_file.write_text(fallback_app, encoding='utf-8')
        outputs.append("src/App.tsx")

    # Generate index.tsx
    index_content = """import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);"""

    index_file = src_dir / "index.tsx"
    index_file.write_text(index_content, encoding='utf-8')
    outputs.append("src/index.tsx")

    # Generate CSS
    css_content = """* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f5f5f5;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  text-align: center;
}

main {
  flex: 1;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

@media (max-width: 768px) {
  .App-header {
    padding: 15px;
  }

  main {
    padding: 15px;
  }
}"""

    css_file = src_dir / "App.css"
    css_file.write_text(css_content, encoding='utf-8')
    outputs.append("src/App.css")

    index_css_file = src_dir / "index.css"
    index_css_file.write_text("/* Global styles */\n" + css_content, encoding='utf-8')
    outputs.append("src/index.css")

    return outputs

async def generate_backend_code(prompt: str, project_dir: Path, architecture_context: str) -> List[str]:
    """Generate backend Express.js code"""
    import openai

    outputs = []

    # Create server directory
    server_dir = project_dir / "server"
    server_dir.mkdir(exist_ok=True)

    # Generate main server file
    server_prompt = f"""Create an Express.js server with TypeScript for this project:

Project: {prompt}
Architecture: {architecture_context[:500]}

Requirements:
1. Include middleware for security, CORS, and JSON parsing
2. Create RESTful API endpoints based on the project description
3. Include error handling middleware
4. Add health check endpoint
5. Include proper logging
6. Use TypeScript

Return only the server code."""

    try:
        client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        server_response = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are an expert Node.js/Express developer. Create production-ready server code with TypeScript."},
                {"role": "user", "content": server_prompt}
            ],
            max_tokens=2000,
            temperature=0.7
        )

        server_content = server_response.choices[0].message.content

        # Clean up the response
        if "```" in server_content:
            start = server_content.find("```")
            end = server_content.rfind("```")
            if start != -1 and end != -1 and end > start:
                server_content = server_content[start+3:end].strip()
                if server_content.startswith("javascript") or server_content.startswith("typescript"):
                    server_content = server_content.split('\n', 1)[1]

        server_file = server_dir / "index.js"
        server_file.write_text(server_content, encoding='utf-8')
        outputs.append("server/index.js")

    except Exception as e:
        # Fallback server
        fallback_server = """const express = require('express');
const cors = require('cors');
const helmet = require('helmet');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'Aetherforge Generated API'
  });
});

// API routes
app.get('/api', (req, res) => {
  res.json({ message: 'Welcome to your Aetherforge generated API!' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/api/health`);
});"""

        server_file = server_dir / "index.js"
        server_file.write_text(fallback_server, encoding='utf-8')
        outputs.append("server/index.js")

    return outputs

async def generate_config_files(project_dir: Path) -> List[str]:
    """Generate configuration files"""
    outputs = []

    # Generate .env.example
    env_example = """# Environment Configuration
NODE_ENV=development
PORT=3001

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# API Keys
API_KEY=your_api_key_here

# Security
JWT_SECRET=your_jwt_secret_here

# External Services
EXTERNAL_API_URL=https://api.example.com
"""

    env_file = project_dir / ".env.example"
    env_file.write_text(env_example, encoding='utf-8')
    outputs.append(".env.example")

    # Generate .gitignore
    gitignore_content = """# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Temporary folders
tmp/
temp/
"""

    gitignore_file = project_dir / ".gitignore"
    gitignore_file.write_text(gitignore_content, encoding='utf-8')
    outputs.append(".gitignore")

    # Generate Dockerfile
    dockerfile_content = """FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
  CMD curl -f http://localhost:3000/api/health || exit 1

# Start the application
CMD ["npm", "start"]
"""

    dockerfile = project_dir / "Dockerfile"
    dockerfile.write_text(dockerfile_content, encoding='utf-8')
    outputs.append("Dockerfile")

    # Generate docker-compose.yml
    compose_content = """version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - db
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: aetherforge_db
      POSTGRES_USER: aetherforge
      POSTGRES_PASSWORD: password123
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

volumes:
  postgres_data:
"""

    compose_file = project_dir / "docker-compose.yml"
    compose_file.write_text(compose_content, encoding='utf-8')
    outputs.append("docker-compose.yml")

    return outputs

async def generate_deployment_guide(prompt: str, project_dir: Path) -> str:
    """Generate deployment guide"""
    return f"""# Deployment Guide

Generated: {datetime.now().isoformat()}

## Project: {prompt}

## Prerequisites
- Node.js 18+
- Docker (optional)
- Git

## Local Development

### 1. Install Dependencies
```bash
npm install
```

### 2. Environment Setup
```bash
cp .env.example .env
# Edit .env with your configuration
```

### 3. Start Development Server
```bash
# Start both frontend and backend
npm run dev

# Or start separately
npm run dev:client  # Frontend on port 3000
npm run dev:server  # Backend on port 3001
```

## Production Deployment

### Docker Deployment (Recommended)
```bash
# Build and run with Docker Compose
docker-compose up --build -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Manual Deployment
```bash
# Install production dependencies
npm ci --only=production

# Build the application
npm run build

# Start production server
npm start
```

## Environment Variables

Required environment variables:
- `NODE_ENV` - Set to 'production' for production
- `PORT` - Port for the server (default: 3001)
- `DATABASE_URL` - Database connection string

Optional:
- `JWT_SECRET` - For authentication
- `API_KEY` - For external services

## Health Checks

- Application: http://localhost:3001/api/health
- Database: Check connection in application logs

## Monitoring

### Logs
```bash
# Docker logs
docker-compose logs -f app

# PM2 logs (if using PM2)
pm2 logs
```

### Performance
- Monitor CPU and memory usage
- Check database connection pool
- Monitor API response times

## Scaling

### Horizontal Scaling
```bash
# Scale with Docker Compose
docker-compose up --scale app=3

# Or use a load balancer like Nginx
```

### Database Scaling
- Consider read replicas for high read loads
- Implement connection pooling
- Use database indexing for performance

## Security Checklist

- [ ] Environment variables are properly set
- [ ] HTTPS is enabled in production
- [ ] Database credentials are secure
- [ ] API rate limiting is configured
- [ ] Input validation is implemented
- [ ] Security headers are set (Helmet.js)

## Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   lsof -ti:3001 | xargs kill -9
   ```

2. **Database connection failed**
   - Check DATABASE_URL format
   - Ensure database server is running
   - Verify credentials

3. **Build failures**
   - Clear node_modules: `rm -rf node_modules && npm install`
   - Check Node.js version compatibility

### Support
- Check application logs for detailed error messages
- Verify all environment variables are set
- Ensure all services are running and accessible
"""

async def finalize_project(project_path: str, project_id: str, trail_id: str):
    """Finalize the project with summary and deployment instructions"""
    try:
        project_dir = Path(project_path)

        # Create final README.md
        readme_content = f"""# {project_dir.name}

## Project Overview
This project was generated by **Aetherforge** - an Autonomous AI Software Creation System.

**Project ID**: {project_id}
**Generated**: {datetime.now().isoformat()}

## Features
- User authentication and authorization
- RESTful API with Express.js
- React frontend with TypeScript
- PostgreSQL database with Prisma ORM
- Comprehensive testing suite
- Docker containerization ready

## Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 15+
- Docker (optional)

### Installation
```bash
# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your database credentials

# Run database migrations
npx prisma migrate dev

# Start development servers
npm run dev
```

### Docker Setup
```bash
# Build and run with Docker Compose
docker-compose up --build
```

## Project Structure
- `src/` - Frontend React application
- `server/` - Backend Express.js API
- `tests/` - Test suites
- `docs/` - Documentation
- `docker/` - Docker configurations

## API Endpoints
- `GET /api/health` - Health check
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/user/profile` - Get user profile

## Testing
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

## Deployment
See `docs/deployment_guide.md` for detailed deployment instructions.

## Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License
MIT License

---
Generated by Aetherforge v0.1.0
"""

        readme_file = project_dir / "README.md"
        readme_file.write_text(readme_content, encoding='utf-8')

        # Create deployment guide
        deployment_content = f"""# Deployment Guide

## Production Deployment

### Environment Setup
1. Set up production server (Ubuntu 20.04+ recommended)
2. Install Node.js 18+, PostgreSQL 15+, and Docker
3. Configure firewall (ports 80, 443, 3000, 3001)

### Database Setup
```bash
# Create production database
sudo -u postgres createdb {project_dir.name.lower()}_prod

# Run migrations
NODE_ENV=production npx prisma migrate deploy
```

### Application Deployment
```bash
# Build the application
npm run build

# Start with PM2
npm install -g pm2
pm2 start ecosystem.config.js --env production
```

### Docker Deployment
```bash
# Build production image
docker build -t {project_dir.name.lower()}:latest .

# Run with docker-compose
docker-compose -f docker-compose.prod.yml up -d
```

### Monitoring
- Set up Prometheus and Grafana for metrics
- Configure log aggregation with ELK stack
- Set up health checks and alerts

### Security Checklist
- [ ] SSL/TLS certificates configured
- [ ] Environment variables secured
- [ ] Database access restricted
- [ ] API rate limiting enabled
- [ ] Security headers configured
- [ ] Regular security updates scheduled

Generated by: Aetherforge
Date: {datetime.now().isoformat()}
"""

        deployment_file = project_dir / "docs" / "deployment_guide.md"
        deployment_file.parent.mkdir(exist_ok=True)
        deployment_file.write_text(deployment_content, encoding='utf-8')

        # Update project metadata
        metadata_file = project_dir / ".aetherforge.json"
        if metadata_file.exists():
            metadata = json.loads(metadata_file.read_text())
        else:
            metadata = {}

        metadata.update({
            "status": "completed",
            "completed_at": datetime.now().isoformat(),
            "trail_id": trail_id,
            "final_outputs": [
                "README.md",
                "docs/deployment_guide.md",
                "src/",
                "server/",
                "tests/",
                "package.json"
            ]
        })

        metadata_file.write_text(json.dumps(metadata, indent=2), encoding='utf-8')

        drop_pheromone("project_finalized", {
            "project_id": project_id,
            "project_path": project_path,
            "final_status": "completed",
            "documentation_created": True
        }, project_id=project_id, trail_id=trail_id)

    except Exception as e:
        logger.error("Failed to finalize project", project_id=project_id, error=str(e))
        drop_pheromone("project_finalization_failed", {
            "project_id": project_id,
            "error": str(e)
        }, project_id=project_id, trail_id=trail_id)
        raise

def get_default_agent_team(workflow: str) -> Dict[str, Any]:
    """Get a default agent team when Archon is not available"""
    return {
        "agents": [
            {"role": "analyst", "name": "Requirements Analyst", "capabilities": ["requirements_analysis", "user_story_creation"]},
            {"role": "architect", "name": "System Architect", "capabilities": ["system_design", "technology_selection"]},
            {"role": "developer", "name": "Full Stack Developer", "capabilities": ["frontend_development", "backend_development"]},
            {"role": "qa", "name": "Quality Assurance", "capabilities": ["testing", "validation"]}
        ],
        "workflow": workflow,
        "coordination_strategy": "sequential"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)