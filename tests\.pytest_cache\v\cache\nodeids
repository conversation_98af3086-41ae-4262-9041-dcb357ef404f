["test_project_generator_comprehensive.py::TestProjectGenerator::test_api_manager_fallback", "test_project_generator_comprehensive.py::TestProjectGenerator::test_backend_code_generation", "test_project_generator_comprehensive.py::TestProjectGenerator::test_config_files_generation", "test_project_generator_comprehensive.py::TestProjectGenerator::test_different_project_types", "test_project_generator_comprehensive.py::TestProjectGenerator::test_documentation_generation", "test_project_generator_comprehensive.py::TestProjectGenerator::test_error_handling", "test_project_generator_comprehensive.py::TestProjectGenerator::test_frontend_code_generation", "test_project_generator_comprehensive.py::TestProjectGenerator::test_generate_project_basic", "test_project_generator_comprehensive.py::TestProjectGenerator::test_generate_project_with_api_manager", "test_project_generator_comprehensive.py::TestProjectGenerator::test_package_config_generation", "test_project_generator_comprehensive.py::TestProjectGenerator::test_project_generator_initialization", "test_project_generator_comprehensive.py::TestProjectGenerator::test_project_metadata_creation", "test_project_generator_comprehensive.py::TestProjectGenerator::test_project_structure_creation", "test_project_generator_comprehensive.py::TestProjectGeneratorIntegration::test_full_project_generation_workflow"]