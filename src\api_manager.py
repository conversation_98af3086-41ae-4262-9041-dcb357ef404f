"""
Enhanced API Manager for Aetherforge
Provides robust API integration with multiple providers, fallback mechanisms, 
retry logic, and rate limiting.
"""

import asyncio
import time
import logging
import os
from typing import Dict, Any, List, Optional, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import json
from pathlib import Path
import aiohttp
import openai
from openai import AsyncOpenAI
import anthropic
from anthropic import AsyncAnthropic

logger = logging.getLogger(__name__)

class APIProvider(Enum):
    """Supported API providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    LOCAL = "local"
    OLLAMA = "ollama"

@dataclass
class APIConfig:
    """Configuration for API providers"""
    provider: APIProvider
    api_key: str
    base_url: Optional[str] = None
    model: str = "gpt-4"
    max_tokens: int = 2000
    temperature: float = 0.7
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    rate_limit_rpm: int = 60  # requests per minute

@dataclass
class RateLimiter:
    """Rate limiter for API calls"""
    max_requests: int
    time_window: int = 60  # seconds
    requests: List[float] = field(default_factory=list)
    
    def can_make_request(self) -> bool:
        """Check if we can make a request within rate limits"""
        now = time.time()
        # Remove old requests outside the time window
        self.requests = [req_time for req_time in self.requests 
                        if now - req_time < self.time_window]
        return len(self.requests) < self.max_requests
    
    def record_request(self):
        """Record a new request"""
        self.requests.append(time.time())
    
    def time_until_next_request(self) -> float:
        """Get time to wait until next request is allowed"""
        if self.can_make_request():
            return 0.0
        
        now = time.time()
        oldest_request = min(self.requests)
        return self.time_window - (now - oldest_request)

class APIError(Exception):
    """Base API error"""
    def __init__(self, message: str, provider: APIProvider, retryable: bool = True):
        super().__init__(message)
        self.provider = provider
        self.retryable = retryable

class QuotaExceededError(APIError):
    """API quota exceeded error"""
    def __init__(self, provider: APIProvider):
        super().__init__(f"API quota exceeded for {provider.value}", provider, False)

class RateLimitError(APIError):
    """Rate limit exceeded error"""
    def __init__(self, provider: APIProvider, retry_after: float = 60):
        super().__init__(f"Rate limit exceeded for {provider.value}", provider, True)
        self.retry_after = retry_after

class APIManager:
    """Enhanced API manager with multi-provider support and robust error handling"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.providers: Dict[APIProvider, APIConfig] = {}
        self.clients: Dict[APIProvider, Any] = {}
        self.rate_limiters: Dict[APIProvider, RateLimiter] = {}
        self.fallback_order: List[APIProvider] = []
        
        # Load configuration
        self._load_configuration(config_file)
        self._initialize_clients()
        self._setup_rate_limiters()
    
    def _load_configuration(self, config_file: Optional[str] = None) -> None:
        """
        Load API configuration from file or environment variables.

        Args:
            config_file: Optional path to JSON configuration file

        Note:
            Configuration is loaded in the following priority:
            1. Configuration file (if provided and exists)
            2. Environment variables
            3. Default values
        """
        
        # Default configurations
        default_configs = {
            APIProvider.OPENAI: APIConfig(
                provider=APIProvider.OPENAI,
                api_key=os.getenv("OPENAI_API_KEY", ""),
                model=os.getenv("OPENAI_MODEL", "gpt-4"),
                max_tokens=int(os.getenv("OPENAI_MAX_TOKENS", "2000")),
                rate_limit_rpm=int(os.getenv("OPENAI_RATE_LIMIT", "60"))
            ),
            APIProvider.ANTHROPIC: APIConfig(
                provider=APIProvider.ANTHROPIC,
                api_key=os.getenv("ANTHROPIC_API_KEY", ""),
                model=os.getenv("ANTHROPIC_MODEL", "claude-3-sonnet-20240229"),
                max_tokens=int(os.getenv("ANTHROPIC_MAX_TOKENS", "2000")),
                rate_limit_rpm=int(os.getenv("ANTHROPIC_RATE_LIMIT", "60"))
            ),
            APIProvider.LOCAL: APIConfig(
                provider=APIProvider.LOCAL,
                api_key="local",
                base_url=os.getenv("LOCAL_API_URL", "http://localhost:11434"),
                model=os.getenv("LOCAL_MODEL", "llama2"),
                rate_limit_rpm=int(os.getenv("LOCAL_RATE_LIMIT", "120"))
            )
        }
        
        # Load from config file if provided
        if config_file and Path(config_file).exists():
            try:
                with open(config_file, 'r') as f:
                    file_config = json.load(f)
                    # Update default configs with file configs
                    for provider_name, config_data in file_config.items():
                        provider = APIProvider(provider_name)
                        if provider in default_configs:
                            # Update existing config
                            for key, value in config_data.items():
                                if hasattr(default_configs[provider], key):
                                    setattr(default_configs[provider], key, value)
            except Exception as e:
                logger.warning(f"Failed to load config file {config_file}: {e}")
        
        # Only add providers with valid API keys
        for provider, config in default_configs.items():
            if config.api_key and config.api_key != "":
                self.providers[provider] = config
                logger.info(f"Configured API provider: {provider.value}")
        
        # Set fallback order based on available providers
        self.fallback_order = [
            APIProvider.OPENAI,
            APIProvider.ANTHROPIC,
            APIProvider.LOCAL
        ]
        self.fallback_order = [p for p in self.fallback_order if p in self.providers]
        
        if not self.providers:
            logger.warning("No API providers configured with valid keys")
    
    def _initialize_clients(self):
        """Initialize API clients for each provider"""
        for provider, config in self.providers.items():
            try:
                if provider == APIProvider.OPENAI:
                    self.clients[provider] = AsyncOpenAI(
                        api_key=config.api_key,
                        base_url=config.base_url,
                        timeout=config.timeout
                    )
                elif provider == APIProvider.ANTHROPIC:
                    self.clients[provider] = AsyncAnthropic(
                        api_key=config.api_key,
                        timeout=config.timeout
                    )
                elif provider == APIProvider.LOCAL:
                    # For local/Ollama, we'll use aiohttp
                    self.clients[provider] = aiohttp.ClientSession(
                        timeout=aiohttp.ClientTimeout(total=config.timeout)
                    )
                
                logger.info(f"Initialized client for {provider.value}")
            except Exception as e:
                logger.error(f"Failed to initialize client for {provider.value}: {e}")
                # Remove provider if client initialization fails
                if provider in self.providers:
                    del self.providers[provider]
    
    def _setup_rate_limiters(self):
        """Setup rate limiters for each provider"""
        for provider, config in self.providers.items():
            self.rate_limiters[provider] = RateLimiter(
                max_requests=config.rate_limit_rpm,
                time_window=60
            )
    
    async def _wait_for_rate_limit(self, provider: APIProvider):
        """Wait if rate limit is exceeded"""
        rate_limiter = self.rate_limiters.get(provider)
        if rate_limiter and not rate_limiter.can_make_request():
            wait_time = rate_limiter.time_until_next_request()
            logger.info(f"Rate limit reached for {provider.value}, waiting {wait_time:.1f}s")
            await asyncio.sleep(wait_time)
    
    async def _make_request_with_retry(
        self, 
        provider: APIProvider, 
        request_func: Callable,
        *args, 
        **kwargs
    ) -> Any:
        """Make API request with exponential backoff retry"""
        config = self.providers[provider]
        
        for attempt in range(config.max_retries + 1):
            try:
                # Check rate limit
                await self._wait_for_rate_limit(provider)
                
                # Record request
                self.rate_limiters[provider].record_request()
                
                # Make request
                result = await request_func(*args, **kwargs)
                return result
                
            except Exception as e:
                error_msg = str(e).lower()
                
                # Check for quota exceeded
                if "quota" in error_msg or "insufficient_quota" in error_msg:
                    raise QuotaExceededError(provider)
                
                # Check for rate limit
                if "rate" in error_msg and "limit" in error_msg:
                    retry_after = 60  # Default retry after 60 seconds
                    raise RateLimitError(provider, retry_after)
                
                # If this is the last attempt, raise the error
                if attempt == config.max_retries:
                    raise APIError(f"API request failed after {config.max_retries} retries: {e}", provider)
                
                # Exponential backoff
                wait_time = config.retry_delay * (2 ** attempt)
                logger.warning(f"API request failed (attempt {attempt + 1}), retrying in {wait_time}s: {e}")
                await asyncio.sleep(wait_time)
        
        raise APIError(f"API request failed after all retries", provider)

    async def generate_text(
        self, 
        messages: List[Dict[str, str]], 
        preferred_provider: Optional[APIProvider] = None,
        **kwargs
    ) -> str:
        """Generate text using the best available provider"""
        
        # Determine provider order
        providers_to_try = []
        if preferred_provider and preferred_provider in self.providers:
            providers_to_try.append(preferred_provider)
        
        # Add fallback providers
        for provider in self.fallback_order:
            if provider not in providers_to_try:
                providers_to_try.append(provider)
        
        if not providers_to_try:
            raise APIError("No API providers available", APIProvider.OPENAI, False)
        
        last_error = None
        
        for provider in providers_to_try:
            try:
                logger.debug(f"Attempting text generation with {provider.value}")
                result = await self._generate_with_provider(provider, messages, **kwargs)
                logger.info(f"Successfully generated text with {provider.value}")
                return result
                
            except QuotaExceededError as e:
                logger.warning(f"Quota exceeded for {provider.value}, trying next provider")
                last_error = e
                continue
                
            except RateLimitError as e:
                logger.warning(f"Rate limit exceeded for {provider.value}, trying next provider")
                last_error = e
                continue
                
            except APIError as e:
                if not e.retryable:
                    logger.error(f"Non-retryable error with {provider.value}: {e}")
                    last_error = e
                    continue
                else:
                    logger.warning(f"Retryable error with {provider.value}: {e}")
                    last_error = e
                    continue
        
        # If we get here, all providers failed
        if last_error:
            raise last_error
        else:
            raise APIError("All API providers failed", APIProvider.OPENAI, False)

    async def _generate_with_provider(
        self, 
        provider: APIProvider, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> str:
        """Generate text with a specific provider"""
        config = self.providers[provider]
        client = self.clients[provider]
        
        # Merge kwargs with config defaults
        generation_params = {
            "max_tokens": kwargs.get("max_tokens", config.max_tokens),
            "temperature": kwargs.get("temperature", config.temperature),
        }
        
        if provider == APIProvider.OPENAI:
            return await self._generate_openai(client, config, messages, generation_params)
        elif provider == APIProvider.ANTHROPIC:
            return await self._generate_anthropic(client, config, messages, generation_params)
        elif provider == APIProvider.LOCAL:
            return await self._generate_local(client, config, messages, generation_params)
        else:
            raise APIError(f"Unsupported provider: {provider.value}", provider, False)

    async def _generate_openai(self, client: AsyncOpenAI, config: APIConfig, messages: List[Dict[str, str]], params: Dict[str, Any]) -> str:
        """Generate text using OpenAI API"""
        async def request_func():
            response = await client.chat.completions.create(
                model=config.model,
                messages=messages,
                max_tokens=params["max_tokens"],
                temperature=params["temperature"]
            )
            return response.choices[0].message.content
        
        return await self._make_request_with_retry(APIProvider.OPENAI, request_func)

    async def _generate_anthropic(self, client: AsyncAnthropic, config: APIConfig, messages: List[Dict[str, str]], params: Dict[str, Any]) -> str:
        """Generate text using Anthropic API"""
        async def request_func():
            # Convert messages format for Anthropic
            system_message = ""
            user_messages = []
            
            for msg in messages:
                if msg["role"] == "system":
                    system_message = msg["content"]
                else:
                    user_messages.append(msg)
            
            response = await client.messages.create(
                model=config.model,
                max_tokens=params["max_tokens"],
                temperature=params["temperature"],
                system=system_message,
                messages=user_messages
            )
            return response.content[0].text
        
        return await self._make_request_with_retry(APIProvider.ANTHROPIC, request_func)

    async def _generate_local(self, client: aiohttp.ClientSession, config: APIConfig, messages: List[Dict[str, str]], params: Dict[str, Any]) -> str:
        """Generate text using local API (Ollama)"""
        async def request_func():
            # Convert to Ollama format
            prompt = "\n".join([f"{msg['role']}: {msg['content']}" for msg in messages])
            
            payload = {
                "model": config.model,
                "prompt": prompt,
                "options": {
                    "num_predict": params["max_tokens"],
                    "temperature": params["temperature"]
                }
            }
            
            async with client.post(f"{config.base_url}/api/generate", json=payload) as response:
                if response.status != 200:
                    raise Exception(f"Local API error: {response.status}")
                
                result = await response.json()
                return result.get("response", "")
        
        return await self._make_request_with_retry(APIProvider.LOCAL, request_func)

    async def close(self):
        """Close all API clients"""
        for provider, client in self.clients.items():
            try:
                if hasattr(client, 'close'):
                    await client.close()
            except Exception as e:
                logger.warning(f"Error closing client for {provider.value}: {e}")

    def get_available_providers(self) -> List[APIProvider]:
        """Get list of available providers"""
        return list(self.providers.keys())

    def get_provider_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all providers"""
        status = {}
        for provider, config in self.providers.items():
            rate_limiter = self.rate_limiters[provider]
            status[provider.value] = {
                "configured": True,
                "model": config.model,
                "rate_limit_remaining": config.rate_limit_rpm - len(rate_limiter.requests),
                "can_make_request": rate_limiter.can_make_request()
            }
        return status

# Global API manager instance
_api_manager: Optional[APIManager] = None

def get_api_manager() -> APIManager:
    """Get global API manager instance"""
    global _api_manager
    if _api_manager is None:
        _api_manager = APIManager()
    return _api_manager

async def generate_text(messages: List[Dict[str, str]], **kwargs) -> str:
    """Convenience function for text generation"""
    api_manager = get_api_manager()
    return await api_manager.generate_text(messages, **kwargs)
